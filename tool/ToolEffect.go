package tool

import (
	"go-nakama-poke/proto/MainServer"
	"strings"
)

type EffectValue int32

const (
	Owner    EffectValue = 0 //自己的增幅（代表可以获取百分百加成
	Party    EffectValue = 1 //队伍的增幅 （可以获取百分之10的加成
	NearTeam EffectValue = 2 //附近的增幅（可以获取百分之5的加成
)

// 用于获取增幅效果的类
func GetEffectValueByTrainer(trainer *MainServer.Trainer, effectType MainServer.TrainerEquipmentEffectType, valueType EffectValue) float32 {
	if trainer == nil || trainer.Decoration == nil {
		return 0
	}

	var totalValue float32 = 0

	equipments := []*MainServer.TrainerEquipmentInfo{
		trainer.Decoration.EquipmentTitle,
		trainer.Decoration.EquipmentCard,
		trainer.Decoration.EquipmentRide,
		trainer.Decoration.EquipmentPokeball,
		trainer.Decoration.EquipmentBadge,
		trainer.Decoration.EquipmentAmulet,
	}

	for _, equipment := range equipments {
		if equipment != nil && equipment.EffectInfo != nil && equipment.EffectInfo.EquipmentEffect != nil {
			if value, exists := equipment.EffectInfo.EquipmentEffect[int32(effectType)]; exists {
				effectValue := value + (float32(equipment.FortifyCount) * value * 0.5)
				totalValue += effectValue
			}
		}
	}

	switch valueType {
	case Owner:
		return totalValue
	case Party:
		return totalValue * 0.1
	case NearTeam:
		return totalValue * 0.05
	default:
		return 0
	}
}
func GetEffectPokeShineValueByTrainer(trainer *MainServer.Trainer, effectType MainServer.TrainerEquipmentEffectPokeType, valueType EffectValue) float32 {
	if trainer == nil || trainer.Decoration == nil {
		return 0
	}

	var totalValue float32 = 0

	equipments := []*MainServer.TrainerEquipmentInfo{
		trainer.Decoration.EquipmentTitle,
		trainer.Decoration.EquipmentCard,
		trainer.Decoration.EquipmentRide,
		trainer.Decoration.EquipmentPokeball,
		trainer.Decoration.EquipmentBadge,
		trainer.Decoration.EquipmentAmulet,
	}

	for _, equipment := range equipments {
		if equipment != nil && equipment.EffectInfo != nil && equipment.EffectInfo.EquipmentPokeShineEffect != nil {
			if value, exists := equipment.EffectInfo.EquipmentPokeShineEffect[int32(effectType)]; exists {
				effectValue := value + (float32(equipment.FortifyCount) * value * 0.5)
				totalValue += effectValue
			}
		}
	}

	switch valueType {
	case Owner:
		return totalValue
	case Party:
		return totalValue * 0.1
	case NearTeam:
		return totalValue * 0.05
	default:
		return 0
	}
}
func GetEffectPokeIvsValueByTrainer(trainer *MainServer.Trainer, effectType MainServer.TrainerEquipmentEffectPokeType, valueType EffectValue) float32 {
	if trainer == nil || trainer.Decoration == nil {
		return 0
	}

	var totalValue float32 = 0

	equipments := []*MainServer.TrainerEquipmentInfo{
		trainer.Decoration.EquipmentTitle,
		trainer.Decoration.EquipmentCard,
		trainer.Decoration.EquipmentRide,
		trainer.Decoration.EquipmentPokeball,
		trainer.Decoration.EquipmentBadge,
		trainer.Decoration.EquipmentAmulet,
	}

	for _, equipment := range equipments {
		if equipment != nil && equipment.EffectInfo != nil && equipment.EffectInfo.EquipmentPokeIvsEffect != nil {
			if value, exists := equipment.EffectInfo.EquipmentPokeIvsEffect[int32(effectType)]; exists {
				effectValue := value + (float32(equipment.FortifyCount) * value * 0.5)
				totalValue += effectValue
			}
		}
	}

	switch valueType {
	case Owner:
		return totalValue
	case Party:
		return totalValue * 0.1
	case NearTeam:
		return totalValue * 0.05
	default:
		return 0
	}
}

func PokeTypeStringToTrainerEquipmentEffectPokeType(pokeType string) MainServer.TrainerEquipmentEffectPokeType {
	pokeType = strings.ToLower(pokeType)
	switch pokeType {
	case "unknown":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Unknown
	case "normal":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Normal
	case "fire":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fire
	case "water":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Water
	case "electric":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Electric
	case "grass":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Grass
	case "ice":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ice
	case "fighting":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fighting
	case "poison":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Poison
	case "ground":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ground
	case "flying":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Flying
	case "psychic":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Psychic
	case "bug":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Bug
	case "rock":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Rock
	case "ghost":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ghost
	case "dragon":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Dragon
	case "dark":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Dark
	case "steel":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Steel
	case "fairy":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fairy
	case "stellar":
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Stellar
	default:
		return MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Unknown
	}
}
func GetPokeEffectMap(allEffect *MainServer.TrainerEquipmentEffect, localPokeData *MainServer.PSPokemonData) map[MainServer.TrainerEquipmentEffectType]float32 {
	result := make(map[MainServer.TrainerEquipmentEffectType]float32)
	for effectType, value := range allEffect.EquipmentEffect {
		result[MainServer.TrainerEquipmentEffectType(effectType)] = value
	}
	for effectType, value := range allEffect.EquipmentPokeIvsEffect {
		for _, pokeType := range localPokeData.Types {
			if effectType == int32(PokeTypeStringToTrainerEquipmentEffectPokeType(pokeType)) {
				result[MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterGoodEnemy] += value
				break
			}
		}
	}
	for effectType, value := range allEffect.EquipmentPokeShineEffect {
		for _, pokeType := range localPokeData.Types {
			if effectType == int32(PokeTypeStringToTrainerEquipmentEffectPokeType(pokeType)) {
				result[MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterShine] += value
				break
			}
		}
	}
	return result
}
func GetAllEffectsByTrainers(owner *MainServer.Trainer, partyTrainers []*MainServer.Trainer, nearTeamTrainers []*MainServer.Trainer) *MainServer.TrainerEquipmentEffect {
	result := &MainServer.TrainerEquipmentEffect{
		EquipmentEffect:          make(map[int32]float32),
		EquipmentPokeIvsEffect:   make(map[int32]float32),
		EquipmentPokeShineEffect: make(map[int32]float32),
	}

	// 处理 Owner (100% 效果)
	if owner != nil {
		addTrainerEffects(result, owner, 1.0)
	}

	// 处理 Party (10% 效果)
	for _, trainer := range partyTrainers {
		if trainer != nil {
			addTrainerEffects(result, trainer, 0.1)
		}
	}

	// 处理 NearTeam (5% 效果)
	for _, trainer := range nearTeamTrainers {
		if trainer != nil {
			addTrainerEffects(result, trainer, 0.05)
		}
	}

	return result
}

func addTrainerEffects(result *MainServer.TrainerEquipmentEffect, trainer *MainServer.Trainer, multiplier float32) {
	if trainer.Decoration == nil {
		return
	}

	equipments := []*MainServer.TrainerEquipmentInfo{
		trainer.Decoration.EquipmentTitle,
		trainer.Decoration.EquipmentCard,
		trainer.Decoration.EquipmentRide,
		trainer.Decoration.EquipmentPokeball,
		trainer.Decoration.EquipmentBadge,
		trainer.Decoration.EquipmentAmulet,
	}

	for _, equipment := range equipments {
		if equipment == nil || equipment.EffectInfo == nil {
			continue
		}

		// 处理 EquipmentEffect
		if equipment.EffectInfo.EquipmentEffect != nil {
			for effectType, value := range equipment.EffectInfo.EquipmentEffect {
				effectValue := value + (float32(equipment.FortifyCount) * value * 0.5)
				result.EquipmentEffect[effectType] += effectValue * multiplier
			}
		}

		// 处理 EquipmentPokeIvsEffect
		if equipment.EffectInfo.EquipmentPokeIvsEffect != nil {
			for effectType, value := range equipment.EffectInfo.EquipmentPokeIvsEffect {
				effectValue := value + (float32(equipment.FortifyCount) * value * 0.5)
				result.EquipmentPokeIvsEffect[effectType] += effectValue * multiplier
			}
		}

		// 处理 EquipmentPokeShineEffect
		if equipment.EffectInfo.EquipmentPokeShineEffect != nil {
			for effectType, value := range equipment.EffectInfo.EquipmentPokeShineEffect {
				effectValue := value + (float32(equipment.FortifyCount) * value * 0.5)
				result.EquipmentPokeShineEffect[effectType] += effectValue * multiplier
			}
		}
	}
}
