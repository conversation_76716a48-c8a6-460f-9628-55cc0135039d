package poke

import (
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strings"
)

// ConvertPokesToPackedFormat 将 []*MainServer.Poke 转换为 Packed 格式
type PokeBattleInfo struct {
	PokeInfo      *MainServer.Poke
	PokeInitBoost *MainServer.PokeBoostStat
	PokeMinBoost  *MainServer.PokeBoostStat
	ImmunityTypes []MainServer.PokeTypeEnum
	CanCapture    bool
	CaptureRate   int32
}

func ConvertPokesToPackedFormat(battlePokeInfos []*PokeBattleInfo) string {
	var result []string

	for _, battlePokeInfo := range battlePokeInfos {
		battlePoke := battlePokeInfo.PokeInfo
		// 获取 Moves 列表
		var moveNames []string
		for _, move := range battlePoke.Moves {
			if move == nil || move.Name == "" {
				continue
			}
			moveNames = append(moveNames, move.Name)
		}

		// 构造 EVs 字段
		evs := fmt.Sprintf("%d,%d,%d,%d,%d,%d",
			battlePoke.Evs.Hp, battlePoke.Evs.Atk, battlePoke.Evs.Def,
			battlePoke.Evs.Spa, battlePoke.Evs.Spd, battlePoke.Evs.Spe)

		// 构造 IVs 字段
		ivs := fmt.Sprintf("%d,%d,%d,%d,%d,%d",
			battlePoke.Ivs.Hp, battlePoke.Ivs.Atk, battlePoke.Ivs.Def,
			battlePoke.Ivs.Spa, battlePoke.Ivs.Spd, battlePoke.Ivs.Spe)

		// 构造其他字段
		nickName := battlePoke.NickName
		if nickName == "" {
			nickName = battlePoke.Name // 使用宝可梦名称作为默认昵称
		}
		// subHpStr := strconv.FormatInt(int64(battlePoke.HpSub), 10)
		// tidStr := strconv.FormatInt(battlePoke.Tid, 10)
		// idStr := strconv.FormatInt(battlePoke.Id, 10)

		// nickName = nickName + "+" + subHpStr + "+" + tidStr + "+" + idStr
		var initPokeInfo = &MainServer.PSInitPokeInfo{
			Id:            battlePoke.Id,
			Tid:           battlePoke.Tid,
			Name:          battlePoke.Name,
			HpSub:         battlePoke.HpSub,
			Moves:         battlePoke.Moves,
			PokeInitBoost: battlePokeInfo.PokeInitBoost,
			PokeMinBoost:  battlePokeInfo.PokeMinBoost,
			ImmunityTypes: battlePokeInfo.ImmunityTypes,
			CaptureRate:   battlePokeInfo.CaptureRate,
			CanCapture:    battlePokeInfo.CanCapture,
		}
		initPokeInfoStr, err := tool.ProtoToBase64NotCompress(initPokeInfo)
		if err != nil {
			continue
		}
		nickName = nickName + "$" + initPokeInfoStr
		// nature := poke.Nature
		// if nature == MainServer.Nature_NATURE_UNSPECIFIED {
		// 	nature = MainServer.Nature_BASHFUL
		// }
		sysExtra := &MainServer.PokeSysExtra{}
		if battlePoke.SysExtra != nil {
			sysExtra = battlePoke.SysExtra
		}
		shiny := ""
		if battlePoke.Shiny != 0 {
			shiny = "S"
		}
		// 获取道具名称
		itemName := ""
		if battlePoke.ItemInfo != nil {
			itemName = battlePoke.ItemInfo.ItemName
		}
		// pokeInfo, _ := pokepackage.GetPokemonInfo(poke.Name)
		// if !exists {
		// 	return nil, runtime.NewError("pokemon not found", 500)
		// }
		gender := genderToString(battlePoke.Gender)
		nature := natureToString(battlePoke.Nature)
		// 构造一只宝可梦的字符串
		pokeStr := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%d|%d,%s,%s,%s,%d",
			nickName,
			battlePoke.Name,
			itemName,
			battlePoke.Ability,
			strings.Join(moveNames, ","),
			nature,
			evs,
			gender,
			ivs,
			shiny,
			battlePoke.Level,
			battlePoke.Happiness,
			battlePoke.BallName,
			sysExtra.Terastal,
			"",
			sysExtra.DynamaxLevel)

		// 将宝可梦字符串加入结果列表
		result = append(result, pokeStr)
	}

	// 使用 `]` 分隔不同的宝可梦
	return strings.Join(result, "]")
}

// genderToString 将性别转换为字符串
func genderToString(gender MainServer.Gender) string {
	// Gender_GenderNull Gender = 0
	// Gender_M          Gender = 1
	// Gender_F          Gender = 2
	// Gender_N          Gender = 3
	switch gender {
	case MainServer.Gender_M:
		return "M" // Male
	case MainServer.Gender_F:
		return "F" // Female
	default:
		return "" // Genderless
	}
}
func natureToString(nature MainServer.Nature) string {
	switch nature {
	case MainServer.Nature_ADAMANT:
		return "adamant"
	case MainServer.Nature_BASHFUL:
		return "bashful"
	case MainServer.Nature_BOLD:
		return "bold"
	case MainServer.Nature_BRAVE:
		return "brave"
	case MainServer.Nature_CALM:
		return "calm"
	case MainServer.Nature_CAREFUL:
		return "careful"
	case MainServer.Nature_DOCILE:
		return "docile"
	case MainServer.Nature_GENTLE:
		return "gentle"
	case MainServer.Nature_HARDY:
		return "hardy"
	case MainServer.Nature_HASTY:
		return "hasty"
	case MainServer.Nature_IMPISH:
		return "impish"
	case MainServer.Nature_JOLLY:
		return "jolly"
	case MainServer.Nature_LAX:
		return "lax"
	case MainServer.Nature_LONELY:
		return "lonely"
	case MainServer.Nature_MILD:
		return "mild"
	case MainServer.Nature_MODEST:
		return "modest"
	case MainServer.Nature_NAIVE:
		return "naive"
	case MainServer.Nature_NAUGHTY:
		return "naughty"
	case MainServer.Nature_QUIET:
		return "quiet"
	case MainServer.Nature_QUIRKY:
		return "quirky"
	case MainServer.Nature_RASH:
		return "rash"
	case MainServer.Nature_RELAXED:
		return "relaxed"
	case MainServer.Nature_SASSY:
		return "sassy"
	case MainServer.Nature_SERIOUS:
		return "serious"
	case MainServer.Nature_TIMID:
		return "timid"
	default:
		return "bashful"
	}
}
