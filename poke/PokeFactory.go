package poke

import (
	"context"
	"database/sql"
	"go-nakama-poke/config"
	"go-nakama-poke/tool"
	"strings"

	"go-nakama-poke/proto/MainServer"
	"math/rand"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 初始化随机数种子
func init() {
	rand.Seed(time.Now().UnixMilli())
}

type LeaderPokeInfo struct {
	MainPokeName    string   //头目
	SubPokeNameList []string //小弟
	Level           int32
	RandomNum       int32 //随机数用于确定出现的位置之类的
	UpdateTs        int64
	TimeLinePoint   int //0点到6点 point为0，6点到12点 point为1，12点到18点 point为2，18点到24点 point为3
}

var leaderPokeInfo *LeaderPokeInfo

func GetCurrentLeaderPokeInfo() *LeaderPokeInfo {
	return leaderPokeInfo
}

// 开启一个任务，每天0点到6点，6点到12点，12点到18点，18点到24点，随机时间内产生LeaderPokeInfo数据
func StartUpdateLeaderPokeInfoLoop(ctx context.Context, db *sql.DB, logger runtime.Logger) {
	UpdateLeaderPokeInfoIfNeed()
	ticker := time.NewTicker(5 * time.Minute) //5分钟检查一次
	go func() {
		for {
			select {
			case <-ticker.C:
				// tx, err := db.BeginTx(ctx, nil)
				// if err != nil {
				// 	logger.Error("failed to begin transaction: %v", err)
				// 	continue
				// }
				//每天0点到6点，6点到12点，12点到18点，18点到24点，随机时间内产生LeaderPokeInfo数据
				//先判断当前的leaderPokeInfo处于什么时间段，假如从一个时间段过到另一个时间段，则更新
				UpdateLeaderPokeInfoIfNeed()
				// if err != nil {
				// 	tx.Rollback()
				// 	logger.Error("failed to update leader poke info: %v", err)
				// 	continue
				// }
				// err = tx.Commit()
				// if err != nil {
				// 	logger.Error("failed to commit transaction: %v", err)
				// 	continue
				// }
			case <-ctx.Done():
				ticker.Stop()
				return
			}
		}
	}()
}
func UpdateLeaderPokeInfoIfNeed() error {
	nowTimeLinePoint := 0
	nowHour := time.Now().Hour()
	if nowHour >= 6 && nowHour < 12 {
		nowTimeLinePoint = 1
	} else if nowHour >= 12 && nowHour < 18 {
		nowTimeLinePoint = 2
	} else if nowHour >= 18 && nowHour < 24 {
		nowTimeLinePoint = 3
	}
	if leaderPokeInfo == nil {
		return UpdateLeaderPokeInfo(nowTimeLinePoint)
	} else {
		//0点到6点 point为0，6点到12点 point为1，12点到18点 point为2，18点到24点 point为3
		if nowTimeLinePoint != leaderPokeInfo.TimeLinePoint {
			return UpdateLeaderPokeInfo(nowTimeLinePoint)
		}
	}
	return nil
}
func UpdateLeaderPokeInfo(timeLinePoint int) error {
	allTeams := []MainServer.TrainerTeam{
		MainServer.TrainerTeam_TRAINER_TEAM_Rocket,
		MainServer.TrainerTeam_TRAINER_TEAM_Magma,
		MainServer.TrainerTeam_TRAINER_TEAM_Aqua,
		MainServer.TrainerTeam_TRAINER_TEAM_Galactic,
		MainServer.TrainerTeam_TRAINER_TEAM_Plasma,
		MainServer.TrainerTeam_TRAINER_TEAM_Flare,
		MainServer.TrainerTeam_TRAINER_TEAM_Skull,
		MainServer.TrainerTeam_TRAINER_TEAM_Yell,
		MainServer.TrainerTeam_TRAINER_TEAM_Star,
	}
	//随机产出
	randomTeam := allTeams[rand.Intn(len(allTeams))]
	allMiddleCateLevels := []MainServer.PokeCateLevel{
		MainServer.PokeCateLevel_PokeCateLevel_1,
		MainServer.PokeCateLevel_PokeCateLevel_2,
		MainServer.PokeCateLevel_PokeCateLevel_3,
		MainServer.PokeCateLevel_PokeCateLevel_4,
		MainServer.PokeCateLevel_PokeCateLevel_5,
	}
	allHighCateLevels := []MainServer.PokeCateLevel{
		MainServer.PokeCateLevel_PokeCateLevel_First,
		MainServer.PokeCateLevel_PokeCateLevel_Late,
		MainServer.PokeCateLevel_PokeCateLevel_Ultra,
		MainServer.PokeCateLevel_PokeCateLevel_Paradox,
	}
	randomAllCateLevels := allMiddleCateLevels
	//10%的概率产出选择allHighCateLevels
	if rand.Float32() < 0.1 {
		randomAllCateLevels = allHighCateLevels
	}
	randomCateLevel := randomAllCateLevels[rand.Intn(len(randomAllCateLevels))]

	nameList, exists := GetWithinLevel5PokeNameList(randomTeam, randomCateLevel)
	if !exists {
		UpdateLeaderPokeInfo(timeLinePoint)
		return nil
	}
	subPokeNameList := make([]string, 0)
	for i := 0; i < 3; i++ {
		subCateLevel := allMiddleCateLevels[rand.Intn(len(allMiddleCateLevels))]
		nameList, exists := GetWithinLevel5PokeNameList(randomTeam, subCateLevel)
		if !exists {
			continue
		}
		subPokeNameList = append(subPokeNameList, nameList.Pokes[rand.Intn(len(nameList.Pokes))])
	}
	randomPokeName := nameList.Pokes[rand.Intn(len(nameList.Pokes))]
	randomLevel := rand.Int31n(50) + 50
	if randomLevel > 100 {
		randomLevel = 100
	}
	if randomLevel < 60 {
		randomLevel = 60
	}
	leaderPokeInfo = &LeaderPokeInfo{
		MainPokeName:    randomPokeName,
		Level:           randomLevel,
		RandomNum:       rand.Int31n(1000),
		UpdateTs:        time.Now().UnixMilli(),
		TimeLinePoint:   timeLinePoint,
		SubPokeNameList: subPokeNameList,
	}
	return nil
}

// func AppearPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, rid string, count int) ([]*MainServer.Poke, error) {
// 	// 确保 payload 包含正确的 key
// 	// rid, exists := tool.GetStringFromPayload("r", payload) // 假设有一个函数解析 rid
// 	// if !exists {
// 	// 	return "", runtime.NewError("无效的区域ID", 400) // 错误处理，区域ID不存在
// 	// }

// 	// // 解析 count，默认值为1
// 	// count, exists := tool.GetIntFromPayload("c", payload)
// 	// if !exists {
// 	// 	count = 1
// 	// }

// 	regionId := rid
// 	var results []*MainServer.Poke // 初始化结果 slice

// 	for i := 0; i < count; i++ {
// 		// 调用 randomPokeWithItem 获取结果
// 		result, err := randomPokeWithItem(ctx, logger, tx, nk, regionId)
// 		if err != nil {
// 			return results, runtime.NewError("获取 Pokemon 失败: "+err.Error(), 500)
// 		}

// 		// 根据概率确定是否是闪光 Pokemon
// 		if rand.Float64() < config.ShinyProbability {
// 			result.Shiny = 1 // 标记为闪光，整数值
// 		}

// 		// 将结果添加到 results 中
// 		results = append(results, result)
// 	}

// 	// 序列化结果
// 	// resultStr, err := json.Marshal(results)
// 	// if err != nil {
// 	// 	return [], runtime.NewError("结果序列化失败: "+err.Error(), 500)
// 	// }

//		return results, nil
//	}
func GetLoopLeaderInitBoostStat() *MainServer.PokeBoostStat {
	pokeBoostStat := &MainServer.PokeBoostStat{
		Hp:             3,
		Attack:         3,
		Defense:        3,
		Speed:          3,
		SpecialAttack:  3,
		SpecialDefense: 3,
		Accuracy:       3,
		Evasion:        3,
	}
	return pokeBoostStat
}

// 创建循环头目的精灵，会固定刷出4只精灵
func CreateLoopLeaderPokes(ctx context.Context, logger runtime.Logger, tx *sql.Tx, regionKey string, areaKey string, method MainServer.EncounterMethod, allEffect *MainServer.TrainerEquipmentEffect, leaderPokeInfo *LeaderPokeInfo) ([]*MainServer.Poke, error) {
	if leaderPokeInfo == nil {
		return nil, runtime.NewError("leader poke info is nil", 500)
	}
	localPokeInfo, exists := GetPokemonInfo(leaderPokeInfo.MainPokeName)
	if !exists {
		return nil, runtime.NewError("pokemon not found", 500)
	}
	var results []*MainServer.Poke
	createPokeInfo := createBaseCreatePokeInfo(allEffect, localPokeInfo)
	createPokeInfo.Level = leaderPokeInfo.Level
	createPokeInfo.IsLeader = true //头目在创造的时候一定是隐藏特性
	pokeInfo, err := CreatePoke(ctx, tx, createPokeInfo, &MainServer.BornInfo{
		BornType: MainServer.BornInfoType_BornInfoType_LoopLeader,
		RegionInfo: &MainServer.BornReginInfo{
			RegionKey: regionKey,
			AreaKey:   areaKey,
			Method:    method,
		},
	})
	results = append(results, pokeInfo)
	if err != nil {
		return nil, err
	}
	for _, subPokeName := range leaderPokeInfo.SubPokeNameList {
		localPokeInfo, exists := GetPokemonInfo(subPokeName)
		if !exists {
			continue
		}
		subCreatePokeInfo := createBaseCreatePokeInfo(allEffect, localPokeInfo)
		subCreatePokeInfo.Level = createPokeInfo.Level - rand.Int31n(6)
		subCreatePokeInfo.HideAbilityRate = 0.3
		pokeInfo, err := CreatePoke(ctx, tx, subCreatePokeInfo, &MainServer.BornInfo{
			BornType: MainServer.BornInfoType_BornInfoType_LoopLeader,
			RegionInfo: &MainServer.BornReginInfo{
				RegionKey: regionKey,
				AreaKey:   areaKey,
				Method:    method,
			},
		})
		results = append(results, pokeInfo)
		if err != nil {
			return nil, err
		}
	}
	return results, nil
}
func createBaseCreatePokeInfo(allEffect *MainServer.TrainerEquipmentEffect, localPokeInfo *MainServer.PSPokemonData) *MainServer.CreatePokeInfo {
	allEffectMap := tool.GetPokeEffectMap(allEffect, localPokeInfo)
	ivsRate := allEffectMap[MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterGoodEnemy]
	shineRate := allEffectMap[MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterShine]
	vLevel := calculateSteppedIVLevel(ivsRate)
	if shineRate > 0.5 {
		shineRate = 0.5
	}
	createPokeInfo := &MainServer.CreatePokeInfo{
		Name:        localPokeInfo.Name,
		IvsMinV:     vLevel,
		IvsMaxV:     vLevel,
		ShineUpRate: shineRate,
	}
	return createPokeInfo
}
func AppearPokes(ctx context.Context, logger runtime.Logger, tx *sql.Tx, regionKey string, areaKey string, method MainServer.EncounterMethod, count int, allEffect *MainServer.TrainerEquipmentEffect) ([]*MainServer.Poke, error) {
	var results []*MainServer.Poke // 初始化结果 slice

	for i := 0; i < count; i++ {
		// 调用 randomPokeWithItem 获取结果
		pokemonChance, err := GetPokemonByChance(regionKey, areaKey, method)
		if err != nil {
			return nil, err
		}
		localPokeInfo, exists := GetPokemonInfo(pokemonChance.Name)
		if !exists {
			return nil, runtime.NewError("pokemon not found", 500)
		}
		// allEffectMap := tool.GetPokeEffectMap(allEffect, localPokeInfo)
		// ivsRate := allEffectMap[MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterGoodEnemy]
		// shineRate := allEffectMap[MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterShine]
		// vLevel := calculateSteppedIVLevel(ivsRate)
		// if shineRate > 0.5 {
		// 	shineRate = 0.5
		// }
		createPokeInfo := createBaseCreatePokeInfo(allEffect, localPokeInfo)
		createPokeInfo.Level = pokemonChance.Level
		// createPokeInfo := &MainServer.CreatePokeInfo{
		// 	Name:        pokemonChance.Name,
		// 	Level:       pokemonChance.Level,
		// 	IvsMinV:     vLevel,
		// 	IvsMaxV:     vLevel,
		// 	ShineUpRate: shineRate,
		// }
		poke, err := CreatePoke(ctx, tx, createPokeInfo, &MainServer.BornInfo{
			BornType: MainServer.BornInfoType_BornInfoType_Wild,
			RegionInfo: &MainServer.BornReginInfo{
				RegionKey: regionKey,
				AreaKey:   areaKey,
				Method:    method,
			},
		})
		if err != nil {
			return nil, err
		}
		// 将结果添加到 results 中
		results = append(results, poke)
	}
	return results, nil
}

// AppearPoke 获取 Pokemon
// func TestRpcAppearPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", runtime.NewError("开始事务失败:"+err.Error(), 500)
// 	}
// 	defer func() {
// 		if err != nil {
// 			tx.Rollback()
// 		} else {
// 			tx.Commit()
// 		}
// 	}()
// 	// 确保 payload 包含正确的 key
// 	rid, exists := tool.GetStringFromPayload("r", payload) // 假设有一个函数解析 rid
// 	if !exists {
// 		return "", runtime.NewError("无效的区域ID", 400) // 错误处理，区域ID不存在
// 	}

// 	// 解析 count，默认值为1
// 	count, exists := tool.GetIntFromPayload("c", payload)
// 	if !exists {
// 		count = 1
// 	}

// 	regionId := rid
// 	var results []*MainServer.Poke // 初始化结果 slice

// 	for i := 0; i < int(count); i++ {
// 		// 调用 randomPokeWithItem 获取结果
// 		result, err := randomPokeWithItem(ctx, logger, tx, nk, regionId)
// 		if err != nil {
// 			return "", runtime.NewError("获取 Pokemon 失败: "+err.Error(), 500)
// 		}

// 		// 根据概率确定是否是闪光 Pokemon
// 		if rand.Float64() < config.ShinyProbability {
// 			result.Shiny = 1 // 标记为闪光，整数值
// 		}

// 		// 将结果添加到 results 中
// 		results = append(results, result)
// 	}

// 	// 序列化结果
// 	resultStr, err := json.Marshal(results)
// 	if err != nil {
// 		return "", runtime.NewError("结果序列化失败: "+err.Error(), 500)
// 	}

// 	return string(resultStr), nil
// }

// 旧的自己配置的poke
// func randomPokeWithItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, regionId string) (*MainServer.Poke, error) {
// 	// 获取 Region 数据
// 	region, err := GetRegionById(ctx, logger, nk, regionId)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 随机选择一个宝可梦
// 	selectedPoke := randomSelectPoke(region.Pokes)
// 	if selectedPoke == nil {
// 		return nil, runtime.NewError("未找到符合条件的宝可梦", 500)
// 	}

// 	// 随机选择一个物品
// 	selectedItem := randomSelectItem(region, selectedPoke)
// 	selectedItemName := ""
// 	if selectedItem != nil {
// 		selectedItemName = selectedItem.Name
// 	}
// 	return CreatePoke(ctx, tx, selectedPoke.Name, selectedItemName, 5)
// 	// 生成随机 IVs 和 EVs
// 	// ivs := GenerateRandomIVs()
// 	// evs := GenerateRandomEVs()

// 	// 创建并返回最终的宝可梦对象
// 	// finalPoke := &MainServer.Poke{
// 	// 	Name:     selectedPoke.Name,
// 	// 	ItemName: selectedItemName,
// 	// 	Ivs:      ivs,
// 	// 	Evs:      evs,
// 	// 	// Level:  selectedPoke.Level,  // 如果需要，可以根据需求设置Level
// 	// 	// Status: selectedPoke.Status, // 如果有其他字段需要初始化，可以在这里添加
// 	// 	// 根据需求设置其他字段
// 	// }

// 	// return finalPoke, nil
// }

// 如果为空，则随机产生
func getPoke(createPokeInfo *MainServer.CreatePokeInfo) (*MainServer.Poke, error) {
	ivs := createPokeInfo.Ivs
	evs := createPokeInfo.Evs
	ability := createPokeInfo.Ability
	name := createPokeInfo.Name
	item := createPokeInfo.Item
	level := createPokeInfo.Level
	isLeader := createPokeInfo.IsLeader

	pokeInfo, exists := GetPokemonInfo(name)
	if !exists {
		return nil, runtime.NewError("pokemon not found", 500)
	}
	if isLeader {
		// createPokeInfo.IvsMinV = 2
		ivs = GenerateRandomIVs(2)
	} else if ivs == nil {
		vLevel := DetermineVLevelFromCreatePokeInfo(createPokeInfo)
		ivs = GenerateRandomIVs(vLevel)
		// ivs = GenerateRandomIVs(0)
	}
	if evs == nil {
		evs = GenerateRandomEVs()
	}
	if isLeader && pokeInfo.Abilities.H != "" {
		ability = pokeInfo.Abilities.H
	} else if createPokeInfo.HideAbilityRate > 0 && ability == "" && pokeInfo.Abilities.H != "" {
		hRand := rand.Float32()
		if hRand < createPokeInfo.HideAbilityRate {
			ability = pokeInfo.Abilities.H
		}
	} else {
		if ability == "" {
			ability = pokeInfo.Abilities.Key0
			abilityNum := rand.Int31n(3)
			if abilityNum == 1 && pokeInfo.Abilities.Key1 != "" {
				ability = pokeInfo.Abilities.Key1
			}
		} else {
			if ability != pokeInfo.Abilities.Key0 && ability != pokeInfo.Abilities.Key1 && ability != pokeInfo.Abilities.H {
				ability = pokeInfo.Abilities.Key0
			}
		}
	}
	shiny := int32(0)
	shinyProbability := config.ShinyProbability
	if createPokeInfo.ShineUpRate > 0 {
		shinyProbability += shinyProbability * float32(createPokeInfo.ShineUpRate)
	}
	// 根据概率确定是否是闪光 Pokemon
	if rand.Float32() < shinyProbability {
		shiny = 1 // 标记为闪光，整数值
	}
	// if ability == "" {
	// 	ability = pokeInfo.Abilities.Key0
	// 	abilityNum := rand.Int31n(3)
	// 	if abilityNum == 1 && pokeInfo.Abilities.Key1 != "" {
	// 		ability = pokeInfo.Abilities.Key1
	// 	}
	// } else {

	// }
	gender := MainServer.Gender_M
	genderRand := rand.Float32() //小于1大于0的小数
	if pokeInfo.GenderRatio != nil && pokeInfo.GenderRatio.M+pokeInfo.GenderRatio.F > 0 {
		mRate := pokeInfo.GenderRatio.M
		fRate := pokeInfo.GenderRatio.F
		if createPokeInfo.FemaleUpRate > 0 {
			fRate += createPokeInfo.FemaleUpRate
			if fRate-genderRand > 0 {
				gender = MainServer.Gender_M
			} else {
				gender = MainServer.Gender_F
			}
		} else if createPokeInfo.MaleUpRate > 0 {
			mRate += createPokeInfo.MaleUpRate
			if mRate-genderRand > 0 {
				gender = MainServer.Gender_M
			} else {
				gender = MainServer.Gender_F
			}
		} else {
			if pokeInfo.GenderRatio.M-genderRand > 0 {
				gender = MainServer.Gender_M
			} else {
				gender = MainServer.Gender_F
			}
		}
	} else {
		gender = MainServer.Gender_N // 如果没有性别比例信息，默认设置为无性别
	}
	growthRate, exists := pokemonGrowthRate[pokeInfo.GrowthRate]
	if !exists {
		return nil, runtime.NewError("pokemon growthRate not found", 500)
	}

	experience, exists := growthRate.Levels[int32(level)]
	if !exists {
		return nil, runtime.NewError("pokemon growthRate level not found", 500)
	}
	// Experience
	movefilter := &MoveFilter{
		PokemonName: name,
		Gen:         0,
		Level:       int32(level),
		Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
		IsInit:      true,
	}
	moves := GetFilteredMoves(*movefilter)
	var movesList []*MainServer.PokeSimpleMove
	for i, move := range moves {
		if i >= 4 { // 只取前四个元素
			break
		}
		movesList = append(movesList, &MainServer.PokeSimpleMove{Name: move})
	}
	// 创建并返回最终的宝可梦对象
	finalPoke := &MainServer.Poke{
		Name:       name,
		ItemInfo:   &MainServer.PokeItemInfo{ItemName: item},
		Ivs:        ivs,
		Evs:        evs,
		Level:      int32(level),
		Ability:    ability, //随机特性
		Nature:     randomNature(),
		Moves:      movesList,
		Gender:     gender,
		Experience: int64(experience),
		Status:     &MainServer.PokeStatusInfo{}, // 默认状态信息
		BorrowInfo: &MainServer.PokeBorrowInfo{}, // 默认借用信息
		SysExtra: &MainServer.PokeSysExtra{
			Terastal: pokeInfo.Types[0],
		},
		Shiny: shiny, // 闪光标记
		HonorInfo: &MainServer.HonorInfo{
			Leader: isLeader,
		},
		// Born: &MainServer.BornInfo{
		// 	BornTs: time.Now().Unix(),
		// },
		// Level:  selectedPoke.Level,  // 如果需要，可以根据需求设置Level
		// 根据需求设置其他字段
	}
	if createPokeInfo.IsAppointNature {
		finalPoke.Nature = createPokeInfo.Nature
	}
	return finalPoke, nil
}

// func CreateNormalWildPoke(ctx context.Context, tx *sql.Tx, name string, item string, level int32) (*MainServer.Poke, error) {
// 	createPokeInfo := &MainServer.CreatePokeInfo{
// 		Name:  name,
// 		Item:  item,
// 		Level: level,
// 	}
// 	return getPoke(createPokeInfo)
// 	// createPokeInfo.Name = name

// }
func CreatePoke(ctx context.Context, tx *sql.Tx, createPokeInfo *MainServer.CreatePokeInfo, bornInfo *MainServer.BornInfo) (*MainServer.Poke, error) {

	finalPoke, err := getPoke(createPokeInfo)
	if err != nil {
		return nil, err
	}
	bornInfo.BornTs = time.Now().Unix()
	finalPoke.Born = bornInfo
	err = insertPokeData(ctx, tx, finalPoke)
	if err != nil {
		return nil, err
	}
	// gender := pokeInfo.Gender
	// genderRand := rand.Float32() //小于1大于0的小数
	// if pokeInfo.GenderRatio != nil && pokeInfo.GenderRatio.M+pokeInfo.GenderRatio.F > 0 {
	// 	if pokeInfo.GenderRatio.M-genderRand > 0 {
	// 		gender = "M"
	// 	} else {
	// 		gender = "F"
	// 	}
	// } else {
	// 	gender = "N" // 如果没有性别比例信息，默认设置为无性别
	// }
	// // if gender == "" {
	// // 	genderRand := rand.Float32() //小于1大于0的小数
	// // 	if pokeInfo.GenderRatio != nil && pokeInfo.GenderRatio.M+pokeInfo.GenderRatio.F > 0 {
	// // 		if pokeInfo.GenderRatio.M-genderRand > 0 {
	// // 			gender = "M"
	// // 		} else {
	// // 			gender = "F"
	// // 		}
	// // 	}
	// // }
	// growthRate, exists := pokemonGrowthRate[pokeInfo.GrowthRate]
	// if !exists {
	// 	return nil, runtime.NewError("pokemon growthRate not found", 500)
	// }

	// experience, exists := growthRate.Levels[int32(level)]
	// if !exists {
	// 	return nil, runtime.NewError("pokemon growthRate level not found", 500)
	// }
	// // Experience
	// movefilter := &MoveFilter{
	// 	PokemonName: name,
	// 	Gen:         0,
	// 	Level:       int32(level),
	// 	Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
	// 	IsInit:      true,
	// }
	// moves := GetFilteredMoves(*movefilter)
	// var movesList []*MainServer.PokeSimpleMove
	// for i, move := range moves {
	// 	if i >= 4 { // 只取前四个元素
	// 		break
	// 	}
	// 	movesList = append(movesList, &MainServer.PokeSimpleMove{Name: move})
	// }
	// // 创建并返回最终的宝可梦对象
	// finalPoke := &MainServer.Poke{
	// 	Name:       name,
	// 	ItemName:   item,
	// 	Ivs:        ivs,
	// 	Evs:        evs,
	// 	Level:      int32(level),
	// 	Ability:    Ability, //随机特性
	// 	Nature:     randomNature(),
	// 	Moves:      movesList,
	// 	Gender:     gender,
	// 	Experience: int64(experience),
	// 	SysExtra: &MainServer.PokeSysExtra{
	// 		Terastal: pokeInfo.Types[0],
	// 	},
	// 	// Level:  selectedPoke.Level,  // 如果需要，可以根据需求设置Level
	// 	// Status: selectedPoke.Status, // 如果有其他字段需要初始化，可以在这里添加
	// 	// 根据需求设置其他字段
	// }
	// insertPokeData(ctx, tx, finalPoke)
	return finalPoke, nil
}

func randomNature() MainServer.Nature {
	natures := []MainServer.Nature{ // 使用枚举类型
		MainServer.Nature_ADAMANT,
		MainServer.Nature_BASHFUL,
		MainServer.Nature_BOLD,
		MainServer.Nature_BRAVE,
		MainServer.Nature_CALM,
		MainServer.Nature_CAREFUL,
		MainServer.Nature_DOCILE,
		MainServer.Nature_GENTLE,
		MainServer.Nature_HARDY,
		MainServer.Nature_HASTY,
		MainServer.Nature_IMPISH,
		MainServer.Nature_JOLLY,
		MainServer.Nature_LAX,
		MainServer.Nature_LONELY,
		MainServer.Nature_MILD,
		MainServer.Nature_MODEST,
		MainServer.Nature_NAIVE,
		MainServer.Nature_NAUGHTY,
		MainServer.Nature_QUIET,
		MainServer.Nature_QUIRKY,
		MainServer.Nature_RASH,
		MainServer.Nature_RELAXED,
		MainServer.Nature_SASSY,
		MainServer.Nature_SERIOUS,
		MainServer.Nature_TIMID,
	}
	// 随机选择一个 Nature
	randomIndex := rand.Intn(len(natures))
	return natures[randomIndex]
}

// 根据概率选择宝可梦
// func randomSelectPoke(pokes []*GameManager.RPoke) *GameManager.RPoke {
// 	totalProb := 0.0
// 	for _, poke := range pokes {
// 		totalProb += float64(poke.P)
// 	}

// 	randomPoint := rand.Float64() * totalProb
// 	currentProb := 0.0

// 	for _, poke := range pokes {
// 		currentProb += float64(poke.P)
// 		if randomPoint <= currentProb {
// 			return poke
// 		}
// 	}
// 	return nil
// }

// // 根据概率选择物品
// func randomSelectItem(region *GameManager.Region, poke *GameManager.RPoke) *GameManager.RPokeItem {
// 	// 先从 Region 的 items 选择
// 	item := randomSelectFromItems(region.Items)
// 	if item != nil {
// 		return item
// 	}

// 	// 如果 Region 没有物品，尝试从 Poke 的 items 选择
// 	return randomSelectFromItems(poke.Items)
// }

// func randomSelectFromItems(items []*GameManager.RPokeItem) *GameManager.RPokeItem {
// 	if len(items) == 0 {
// 		return nil // 如果没有物品，直接返回 nil
// 	}

// 	// 计算所有物品的总概率值
// 	totalProb := 0.0
// 	for _, item := range items {
// 		totalProb += float64(item.P)
// 	}

// 	// 如果总概率为 0，直接返回 nil
// 	if totalProb == 0 {
// 		return nil
// 	}

// 	// 生成一个随机数，范围是 [0, totalProb)
// 	randomPoint := rand.Float64() * totalProb

// 	// 遍历物品并累加概率，找到随机数落在的物品
// 	currentProb := 0.0
// 	var selectedItem *GameManager.RPokeItem
// 	for _, item := range items {
// 		currentProb += float64(item.P)
// 		if randomPoint <= currentProb {
// 			selectedItem = item
// 			break
// 		}
// 	}

// 	// 如果没有选中任何物品，返回 nil
// 	if selectedItem == nil {
// 		return nil
// 	}

// 	// 生成一个新的随机数 [0, 1)，并与选中物品的 P 值进行比较
// 	secondRandom := rand.Float64()
// 	if secondRandom <= float64(selectedItem.P) {
// 		return selectedItem // 随机数小于物品的 P 值，返回该物品
// 	}

// 	// 第二次随机失败，返回 nil
// 	return nil
// }

// 随机生成IVs，每项最大值为31
// fixedVLevel：0 表示随机，1 表示强制生成 1v，2 表示强制生成 2v ...
func GenerateRandomIVs(fixedVLevel int32) *MainServer.PokeStat {
	// 定义各个等级的概率
	vProbabilities := map[int32]float32{
		6: 0.005,
		5: 0.05,
		4: 0.5,
		3: 3,
		2: 7,
		1: 15,
		0: 100, // normal 的概率是剩下的
	}

	// 确定要生成的等级
	var vLevel int32
	if fixedVLevel > 0 {
		// 强制使用指定等级（最大不超过6）
		if fixedVLevel > 6 {
			fixedVLevel = 6
		}
		vLevel = fixedVLevel
	} else {
		// 正常随机
		vLevel = determineVLevel(vProbabilities)
	}

	// 创建一个初始IVs全为随机值（0-31）
	ivs := &MainServer.PokeStat{
		Hp:  rand.Int31n(32),
		Atk: rand.Int31n(32),
		Def: rand.Int31n(32),
		Spa: rand.Int31n(32),
		Spd: rand.Int31n(32),
		Spe: rand.Int31n(32),
	}

	// 如果 vLevel 大于0，则从6项属性中随机选出vLevel个，将其值设置为31
	if vLevel > 0 {
		ivAttributes := []string{"Hp", "Atk", "Def", "Spa", "Spd", "Spe"}

		// 随机打乱属性顺序
		rand.Shuffle(len(ivAttributes), func(i, j int) {
			ivAttributes[i], ivAttributes[j] = ivAttributes[j], ivAttributes[i]
		})

		// 为前vLevel个属性设置满值31
		for i := int32(0); i < vLevel; i++ {
			switch ivAttributes[i] {
			case "Hp":
				ivs.Hp = 31
			case "Atk":
				ivs.Atk = 31
			case "Def":
				ivs.Def = 31
			case "Spa":
				ivs.Spa = 31
			case "Spd":
				ivs.Spd = 31
			case "Spe":
				ivs.Spe = 31
			}
		}
	}

	return ivs
}

func DetermineVLevelFromCreatePokeInfo(createPokeInfo *MainServer.CreatePokeInfo) int32 {
	minV := createPokeInfo.IvsMinV
	maxV := createPokeInfo.IvsMaxV
	vProbMap := createPokeInfo.IvsVMap

	// 如果 minV 和 maxV 都是0
	if minV == 0 && maxV == 0 {
		if len(vProbMap) > 0 {
			vLevel := determineVLevel(vProbMap)
			return vLevel
		} else {
			return 0
		}
	}

	// 如果 maxV 小于 minV，修正一下
	if maxV < minV {
		maxV = minV
	}

	// 如果概率表为空，随机返回 minV~maxV之间的值
	if len(vProbMap) == 0 {
		return minV + int32(rand.Intn(int(maxV-minV+1)))
	}

	// 用概率表决定 vLevel
	vLevel := determineVLevel(vProbMap)

	// 限制在 minV 和 maxV 范围内
	if vLevel < minV {
		vLevel = minV
	}
	if vLevel > maxV {
		vLevel = maxV
	}

	return vLevel
}

// 辅助函数：根据给定的概率分布选择 vLevel (0 表示 normal)
func determineVLevel(probabilities map[int32]float32) int32 {
	// 计算总权重
	var totalWeight float32
	for _, weight := range probabilities {
		totalWeight += weight
	}

	// 生成一个0到总权重之间的随机数
	randValue := rand.Float32() * totalWeight

	// 根据概率分布选择vLevel
	for vLevel, probability := range probabilities {
		if randValue < probability {
			return vLevel
		}
		randValue -= probability
	}

	// 默认返回 normal (0)
	return 0
}

// 随机生成EVs，总和不能超过510，单项最大值为252
func GenerateRandomEVs() *MainServer.PokeStat {
	totalEVs := int32(510)
	evMax := int32(252)
	evs := []int32{0, 0, 0, 0, 0, 0} // 用于存放随机生成的六项EV

	// 随机分配510点EVs
	for totalEVs > 0 {
		for i := 0; i < len(evs); i++ {
			if totalEVs == 0 {
				break
			}
			// 为每个属性分配随机数量的EV
			if evs[i] < evMax {
				evIncrement := rand.Int31n(int32(min(evMax-evs[i], totalEVs)) + 1) // 分配随机的EV值
				evs[i] += evIncrement
				totalEVs -= int32(evIncrement)
			}
		}
	}

	return &MainServer.PokeStat{
		Hp:  evs[0],
		Atk: evs[1],
		Def: evs[2],
		Spa: evs[3],
		Spd: evs[4],
		Spe: evs[5],
	}
}

// 辅助函数，用于获取较小的数值
func min(a, b int32) int32 {
	if a < b {
		return a
	}
	return b
}

//	type BreedingItems struct {
//		RedString     bool                 // 红线
//		ShinyCharm    bool                 // 闪耀护符
//		ShinyDrug     bool                 // 闪光药物
//		ImmortalCharm bool                 // 不死护符
//		StatItems     *MainServer.PokeStat // 能力遗传道具
//	}
func MMoBreedPokemon(ctx context.Context, tx *sql.Tx, father, mother *MainServer.Poke,
	fatherItems, motherItems *MainServer.BreedingItems) ([]*MainServer.Poke, bool, bool, error) {
	// 决定种族
	babyName := determineSpecies(father, mother)

	// 继承技能
	moves := inheritMoves(father, mother)

	// 继承能力值
	ivs := mmoInheritIVs(father, mother, fatherItems, motherItems)

	// 决定特性
	ability := inheritAbility(father, mother)

	// 决定是否为闪光
	// shiny := 0 //determineShiny(father, mother, fatherItems, motherItems)
	shiny := int32(father.Shiny+mother.Shiny) / 2
	// if father.Shiny > 0 && mother.Shiny > 0 {
	// }

	// 决定精灵球
	ballName := inheritBall(father, mother)

	// 决定性格
	nature := mmoInheritNature(father, mother, fatherItems, motherItems)

	terastal := inheritTerastal(father, mother)

	// 计算死亡概率
	// fatherDied, motherDied := true, true
	//测试
	fatherDied, motherDied := false, false

	// 创建新的宝可梦
	baby := &MainServer.Poke{
		Name:     babyName,
		Moves:    moves,
		Ivs:      ivs,
		Ability:  ability,
		Shiny:    shiny,
		BallName: ballName,
		Nature:   nature,
		Level:    1,
		Evs:      &MainServer.PokeStat{},    // 新生宝可梦EVs为0
		Gender:   determineGender(babyName), // 根据种族决定性别
		Egg:      true,
		SysExtra: &MainServer.PokeSysExtra{
			Terastal: terastal,
		},
	}

	// 保存到数据库
	err := insertPokeData(ctx, tx, baby)
	if err != nil {
		return nil, false, false, err
	}

	return []*MainServer.Poke{baby}, fatherDied, motherDied, nil
}
func BreedPokemon(ctx context.Context, tx *sql.Tx, father, mother *MainServer.Poke,
	fatherItems, motherItems *MainServer.BreedingItems) ([]*MainServer.Poke, bool, bool, error) {

	// 决定种族
	babyName := determineSpecies(father, mother)

	// 继承技能
	moves := inheritMoves(father, mother)

	// 继承能力值
	ivs := inheritIVs(father, mother, fatherItems, motherItems)

	// 决定特性
	ability := inheritAbility(father, mother)

	// 决定是否为闪光
	shiny := determineShiny(father, mother, fatherItems, motherItems)

	// 决定精灵球
	ballName := inheritBall(father, mother)

	// 决定性格
	nature := mmoInheritNature(father, mother, fatherItems, motherItems)

	terastal := inheritTerastal(father, mother)

	// 计算死亡概率
	fatherDied, motherDied := calculateDeathProbability(father, mother, ivs,
		fatherItems, motherItems)
	//测试
	fatherDied, motherDied = false, false
	babys := []*MainServer.Poke{}
	// 创建新的宝可梦
	baby := &MainServer.Poke{
		Name:     babyName,
		Moves:    moves,
		Ivs:      ivs,
		Ability:  ability,
		Shiny:    shiny,
		BallName: ballName,
		Nature:   nature,
		Level:    1,
		Evs:      &MainServer.PokeStat{},    // 新生宝可梦EVs为0
		Gender:   determineGender(babyName), // 根据种族决定性别
		Egg:      true,
		SysExtra: &MainServer.PokeSysExtra{
			Terastal: terastal,
		},
	}
	// 保存到数据库
	err := insertPokeData(ctx, tx, baby)
	if err != nil {
		return nil, false, false, err
	}
	babys = append(babys, baby)
	twins := rand.Float64() < 0.1
	if twins { //如果是双胞胎
		err := insertPokeData(ctx, tx, baby)
		if err != nil {
			return nil, false, false, err
		}
		babys = append(babys, baby)
	}

	return babys, fatherDied, motherDied, nil
}

func CanBreed(poke1, poke2 *MainServer.Poke) (*MainServer.Poke, *MainServer.Poke, bool) {
	// 检查是否有不可孵化蛋的蛋组
	poke1Data, _ := GetPokemonInfo(poke1.Name)
	poke2Data, _ := GetPokemonInfo(poke2.Name)

	// 检查是否有不可孵化的蛋组
	for _, group := range poke1Data.EggGroups {
		if group == "Undiscovered" || group == "undiscovered" {
			return poke1, poke2, false
		}
	}
	for _, group := range poke2Data.EggGroups {
		if group == "Undiscovered" || group == "undiscovered" {
			return poke1, poke2, false
		}
	}

	// 如果其中一个是百变怪，直接返回true，不需要检查性别
	if isDitto(poke1) || isDitto(poke2) {
		// 如果poke1是百变怪，返回poke2作为母方
		if isDitto(poke1) {
			return poke2, poke1, true
		}
		// 如果poke2是百变怪，返回poke1作为母方
		return poke1, poke2, true
	}

	// 检查性别
	if poke1.Gender == poke2.Gender {
		return poke1, poke2, false // 同性无法繁殖
	}

	// 确定父方和母方
	var father, mother *MainServer.Poke
	if poke1.Gender == MainServer.Gender_M {
		father, mother = poke1, poke2
	} else {
		father, mother = poke2, poke1
	}

	// 检查蛋组是否匹配
	hasMatchingEggGroup := false
	for _, fatherGroup := range poke1Data.EggGroups {
		for _, motherGroup := range poke2Data.EggGroups {
			if fatherGroup == motherGroup {
				hasMatchingEggGroup = true
				break
			}
		}
		if hasMatchingEggGroup {
			break
		}
	}

	if !hasMatchingEggGroup {
		return father, mother, false
	}

	return father, mother, true
}

func inheritTerastal(father, mother *MainServer.Poke) string {
	//随机father或者mother的terastal
	if rand.Float64() < 0.5 {
		return father.SysExtra.Terastal
	}
	return mother.SysExtra.Terastal
}
func inheritMoves(father, mother *MainServer.Poke) []*MainServer.PokeSimpleMove {
	var moves []*MainServer.PokeSimpleMove
	if !isDitto(father) {
		filter := MoveFilter{
			PokemonName: father.Name,
			Gen:         0,
			Level:       100,
			MinLevel:    1,
			Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_EGG_MOVE},
			IsInit:      false,
		}
		moveStrings := GetFilteredMoves(filter)

		for _, move := range moveStrings {
			for _, m := range father.Moves {
				if m.Name == move {
					moves = append(moves, &MainServer.PokeSimpleMove{Name: move})
				}
			}
		}
	}
	if !isDitto(mother) {
		filter := MoveFilter{
			PokemonName: mother.Name,
			Gen:         0,
			Level:       100,
			MinLevel:    1,
			Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_EGG_MOVE},
			IsInit:      false,
		}
		moveStrings := GetFilteredMoves(filter)

		for _, move := range moveStrings {
			for _, m := range mother.Moves {
				if m.Name == move {
					moves = append(moves, &MainServer.PokeSimpleMove{Name: move})
				}
			}
		}
	}
	// moves里面随机取出4个
	if len(moves) > 4 {
		//随机
		rand.Shuffle(len(moves), func(i, j int) {
			moves[i], moves[j] = moves[j], moves[i]
		})
		moves = moves[:4]
	}
	if len(moves) < 1 {
		filter := MoveFilter{
			PokemonName: mother.Name,
			Gen:         0,
			Level:       1,
			Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
			IsInit:      true,
		}
		moveList := GetFilteredMoves(filter)
		if len(moveList) > 0 {
			moves = append(moves, &MainServer.PokeSimpleMove{Name: moveList[0]})
		} else {
			return []*MainServer.PokeSimpleMove{}
		}
	}
	return moves
}

func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}
func mmoInheritIVs(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) *MainServer.PokeStat {
	ivs := &MainServer.PokeStat{}
	usedStats := &MainServer.PokeStat{}
	// inheritCount := 2

	// if fatherItems.RedString || motherItems.RedString {
	// 	inheritCount = 4
	// }

	// 统一处理父母方的遗传道具
	type parentInfo struct {
		poke  *MainServer.Poke
		items *MainServer.BreedingItems
	}
	parents := []parentInfo{
		{father, fatherItems},
		{mother, motherItems},
	}

	stats := []struct {
		get   func(*MainServer.PokeStat) int32
		set   func(*MainServer.PokeStat, int32)
		getIV func(*MainServer.Poke) int32
	}{
		{func(ps *MainServer.PokeStat) int32 { return ps.Hp }, func(ps *MainServer.PokeStat, v int32) { ps.Hp = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Hp }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Atk }, func(ps *MainServer.PokeStat, v int32) { ps.Atk = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Atk }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Def }, func(ps *MainServer.PokeStat, v int32) { ps.Def = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Def }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spa }, func(ps *MainServer.PokeStat, v int32) { ps.Spa = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spa }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spd }, func(ps *MainServer.PokeStat, v int32) { ps.Spd = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spd }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spe }, func(ps *MainServer.PokeStat, v int32) { ps.Spe = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spe }},
	}

	// 先处理固定遗传
	for _, s := range stats {
		for _, p := range parents {
			if p.items.StatItems != nil && s.get(p.items.StatItems) > 0 && s.get(usedStats) == 0 {
				s.set(usedStats, 1)
				s.set(ivs, s.getIV(p.poke))
				break
			}
		}
	}

	// 统计已使用数
	usedCount := int(usedStats.Hp + usedStats.Atk + usedStats.Def + usedStats.Spa + usedStats.Spd + usedStats.Spe)
	// remainingInherit := minInt(inheritCount-usedCount, 6-usedCount)
	remainingInherit := 6 - usedCount

	// 随机遗传
	for i := 0; i < remainingInherit; i++ {
		available := []int{}
		for idx, s := range stats {
			if s.get(usedStats) == 0 {
				available = append(available, idx)
			}
		}
		if len(available) == 0 {
			break
		}
		choice := available[rand.Intn(len(available))]
		randFloat := rand.Float64()
		fromFather := randFloat < 0.3
		fromMother := randFloat >= 0.3 && randFloat < 0.7
		if fromFather {
			stats[choice].set(ivs, stats[choice].getIV(father))
		} else if fromMother {
			stats[choice].set(ivs, stats[choice].getIV(mother))
		} else {
			stats[choice].set(ivs, (stats[choice].getIV(father)+stats[choice].getIV(mother))/2)
		}
		stats[choice].set(usedStats, 1)
	}

	// 剩下的未遗传项，随机赋值
	// for _, s := range stats {
	// 	if s.get(usedStats) == 0 {
	// 		s.set(ivs, rand.Int31n(32))
	// 	}
	// }

	return ivs
}

func inheritIVs(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) *MainServer.PokeStat {
	ivs := &MainServer.PokeStat{}
	usedStats := &MainServer.PokeStat{}
	inheritCount := 2

	if fatherItems.RedString || motherItems.RedString {
		inheritCount = 4
	}

	// 统一处理父母方的遗传道具
	type parentInfo struct {
		poke  *MainServer.Poke
		items *MainServer.BreedingItems
	}
	parents := []parentInfo{
		{father, fatherItems},
		{mother, motherItems},
	}

	stats := []struct {
		get   func(*MainServer.PokeStat) int32
		set   func(*MainServer.PokeStat, int32)
		getIV func(*MainServer.Poke) int32
	}{
		{func(ps *MainServer.PokeStat) int32 { return ps.Hp }, func(ps *MainServer.PokeStat, v int32) { ps.Hp = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Hp }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Atk }, func(ps *MainServer.PokeStat, v int32) { ps.Atk = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Atk }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Def }, func(ps *MainServer.PokeStat, v int32) { ps.Def = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Def }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spa }, func(ps *MainServer.PokeStat, v int32) { ps.Spa = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spa }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spd }, func(ps *MainServer.PokeStat, v int32) { ps.Spd = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spd }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spe }, func(ps *MainServer.PokeStat, v int32) { ps.Spe = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spe }},
	}

	// 先处理固定遗传
	for _, s := range stats {
		for _, p := range parents {
			if p.items.StatItems != nil && s.get(p.items.StatItems) > 0 && s.get(usedStats) == 0 {
				s.set(usedStats, 1)
				s.set(ivs, s.getIV(p.poke))
				break
			}
		}
	}

	// 统计已使用数
	usedCount := int(usedStats.Hp + usedStats.Atk + usedStats.Def + usedStats.Spa + usedStats.Spd + usedStats.Spe)
	remainingInherit := minInt(inheritCount-usedCount, 6-usedCount)

	// 随机遗传
	for i := 0; i < remainingInherit; i++ {
		available := []int{}
		for idx, s := range stats {
			if s.get(usedStats) == 0 {
				available = append(available, idx)
			}
		}
		if len(available) == 0 {
			break
		}
		choice := available[rand.Intn(len(available))]
		fromFather := rand.Float64() < 0.5
		if fromFather {
			stats[choice].set(ivs, stats[choice].getIV(father))
		} else {
			stats[choice].set(ivs, stats[choice].getIV(mother))
		}
		stats[choice].set(usedStats, 1)
	}

	// 剩下的未遗传项，随机赋值
	for _, s := range stats {
		if s.get(usedStats) == 0 {
			s.set(ivs, rand.Int31n(32))
		}
	}

	return ivs
}

func inheritAbility(father, mother *MainServer.Poke) string {
	// 处理百变怪情况
	if isDitto(father) || isDitto(mother) {
		return getNormalAbility(father, mother)
	}

	// 计算隐藏特性概率
	hiddenAbilityProb := 0.0

	// 获取父母宝可梦的数据
	fatherData, _ := GetPokemonInfo(father.Name)
	motherData, _ := GetPokemonInfo(mother.Name)

	if isHiddenAbility(father.Ability, fatherData) && isHiddenAbility(mother.Ability, motherData) {
		hiddenAbilityProb = 0.6
	} else if isHiddenAbility(father.Ability, fatherData) || isHiddenAbility(mother.Ability, motherData) {
		hiddenAbilityProb = 0.3
	}

	if rand.Float64() < hiddenAbilityProb {
		return getHiddenAbility(father, mother)
	}
	return getNormalAbility(father, mother)
}
func mmoDetermineShiny(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) int32 {
	return int32(father.Shiny+mother.Shiny) / 2
}

func determineShiny(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) int32 {
	if rand.Float64() < 0.5 {
		return int32(father.Shiny+mother.Shiny) / 2
	}
	return 0
	// shinyProb := 1.0 / 10000.0 // 基础概率

	// if father.Shiny == 1 && mother.Shiny == 1 {
	// 	shinyProb = 1.0 / 6000.0
	// } else if father.Shiny == 1 || mother.Shiny == 1 {
	// 	shinyProb = 1.0 / 8000.0
	// }

	// if fatherItems.ShinyCharm || motherItems.ShinyCharm {
	// 	shinyProb = 1.0 / 4000.0
	// }

	// if fatherItems.ShinyDrug || motherItems.ShinyDrug {
	// 	shinyProb = 1.0 / 1000.0
	// }

	// if rand.Float64() < shinyProb {
	// 	return 1
	// }
	// return 0
}

func inheritBall(father, mother *MainServer.Poke) string {
	if rand.Float32() < 0.5 {
		return father.BallName
	}
	return mother.BallName
}
func mmoInheritNature(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) MainServer.Nature {
	if fatherItems.Everstone && motherItems.Everstone {
		rand := rand.Float32()
		if rand < 0.5 {
			return father.Nature
		} else {
			return mother.Nature
		}
	}
	if fatherItems.Everstone {
		return father.Nature
	}
	if motherItems.Everstone {
		return mother.Nature
	}
	rand := rand.Float32()
	if rand < 0.2 {
		return father.Nature
	} else if rand < 0.4 {
		return mother.Nature
	}
	return randomNature()
}

// func inheritNature(father, mother *MainServer.Poke) MainServer.Nature {
// 	rand := rand.Float32()
// 	if rand < 0.4 {
// 		return father.Nature
// 	} else if rand < 0.8 {
// 		return mother.Nature
// 	}
// 	return randomNature()
// }

func calculateDeathProbability(father, mother *MainServer.Poke,
	babyIvs *MainServer.PokeStat, fatherItems, motherItems *MainServer.BreedingItems) (bool, bool) {

	// 如果有不死护符，则不会死亡
	if fatherItems.ImmortalCharm {
		return false, calculateSingleDeathProb(mother, mother.BreedCount, babyIvs)
	}
	if motherItems.ImmortalCharm {
		return calculateSingleDeathProb(father, father.BreedCount, babyIvs), false
	}

	// 检查是否有5项31
	if countMaxIVs(babyIvs) >= 5 {
		return true, true
	}

	// 根据繁殖次数计算死亡概率
	motherDeathProb := 0.35
	switch mother.BreedCount {
	case 2:
		motherDeathProb = 0.6
	case 3:
		motherDeathProb = 0.8
	default:
		if mother.BreedCount >= 4 {
			motherDeathProb = 0.95
		}
	}
	fatherDeathProb := 0.35
	switch father.BreedCount {
	case 2:
		fatherDeathProb = 0.6
	case 3:
		fatherDeathProb = 0.8
	default:
		if father.BreedCount >= 4 {
			fatherDeathProb = 0.95
		}
	}

	return rand.Float64() < fatherDeathProb, rand.Float64() < motherDeathProb
}

// 辅助函数
func setValue(stat *MainServer.PokeStat, name string, value int32) {
	switch name {
	case "Hp":
		stat.Hp = value
	case "Atk":
		stat.Atk = value
	case "Def":
		stat.Def = value
	case "Spa":
		stat.Spa = value
	case "Spd":
		stat.Spd = value
	case "Spe":
		stat.Spe = value
	}
}

func getValue(stat *MainServer.PokeStat, name string) int32 {
	switch name {
	case "Hp":
		return stat.Hp
	case "Atk":
		return stat.Atk
	case "Def":
		return stat.Def
	case "Spa":
		return stat.Spa
	case "Spd":
		return stat.Spd
	case "Spe":
		return stat.Spe
	}
	return 0
}

func countMaxIVs(ivs *MainServer.PokeStat) int {
	count := 0
	if ivs.Hp == 31 {
		count++
	}
	if ivs.Atk == 31 {
		count++
	} else if ivs.Atk == 0 { //攻击为0
		count++
	}
	if ivs.Def == 31 {
		count++
	}
	if ivs.Spa == 31 {
		count++
	}
	if ivs.Spd == 31 {
		count++
	}
	if ivs.Spe == 31 {
		count++
	} else if ivs.Spe == 0 { //速度为0
		count++
	}
	return count
}

func calculateSingleDeathProb(poke *MainServer.Poke, breedCount int32, babyIvs *MainServer.PokeStat) bool {
	if countMaxIVs(babyIvs) >= 5 {
		return true
	}

	deathProb := 0.35
	switch breedCount {
	case 2:
		deathProb = 0.6
	case 3:
		deathProb = 0.8
	default:
		if breedCount >= 4 {
			deathProb = 0.95
		}
	}

	return rand.Float64() < deathProb
}

func isDitto(poke *MainServer.Poke) bool {
	return poke.Name == "Ditto" || poke.Name == "ditto"
}

func determineSpecies(father, mother *MainServer.Poke) string {
	upSpecies := ""
	if isDitto(father) {
		upSpecies = mother.Name
	}
	if upSpecies == "" && isDitto(mother) {
		upSpecies = father.Name
	}
	if upSpecies == "" {
		upSpecies = mother.Name
	}
	pokemonData, exists := GetPokemonInfo(upSpecies)
	if !exists {
		return upSpecies
	}
	if pokemonData.Prevo != "" {
		upSpecies = strings.ToLower(pokemonData.Prevo)
		pokemonData, exists = GetPokemonInfo(upSpecies)
		if !exists {
			return upSpecies
		}
		if pokemonData.Prevo != "" {
			upSpecies = pokemonData.Prevo
		}
	}
	return upSpecies
}

func determineGender(species string) MainServer.Gender {
	pokemonData, exists := GetPokemonInfo(species)
	if !exists {
		return MainServer.Gender_M // 默认返回公性
	}

	// 检查是否有固定性别
	if pokemonData.Gender == "M" {
		return MainServer.Gender_M
	} else if pokemonData.Gender == "F" {
		return MainServer.Gender_F
	} else if pokemonData.Gender == "N" {
		return MainServer.Gender_N
	}

	// 检查是否有性别比例
	if pokemonData.GenderRatio == nil ||
		(pokemonData.GenderRatio.M == 0 && pokemonData.GenderRatio.F == 0) || (pokemonData.GenderRatio.M == -1 && pokemonData.GenderRatio.F == -1) {
		return MainServer.Gender_N // 无性别
	}

	// 根据性别比例随机决定
	randNum := rand.Float32()
	if randNum < pokemonData.GenderRatio.F {
		return MainServer.Gender_F
	}
	return MainServer.Gender_M
}

// 判断是否为隐藏特性
func isHiddenAbility(ability string, pokemonData *MainServer.PSPokemonData) bool {
	if pokemonData == nil {
		return false
	}
	return ability == pokemonData.Abilities.H
}

// 获取普通特性
func getNormalAbility(father, mother *MainServer.Poke) string {
	// 获取非百变怪的宝可梦数据
	var pokemonData *MainServer.PSPokemonData
	if !isDitto(mother) {
		pokemonData, _ = GetPokemonInfo(mother.Name)
	} else {
		pokemonData, _ = GetPokemonInfo(father.Name)
	}

	// 如果数据不存在，返回默认特性
	if pokemonData == nil {
		return "Overgrow" // 默认特性
	}

	// 80%概率继承key0特性，30%概率继承key1特性（如果存在）
	if pokemonData.Abilities.Key1 != "" && rand.Float32() < 0.3 {
		return pokemonData.Abilities.Key1
	}
	return pokemonData.Abilities.Key0
}

// 获取隐藏特性
func getHiddenAbility(father, mother *MainServer.Poke) string {
	// 获取非百变怪的宝可梦数据
	var pokemonData *MainServer.PSPokemonData
	if !isDitto(mother) {
		pokemonData, _ = GetPokemonInfo(mother.Name)
	} else {
		pokemonData, _ = GetPokemonInfo(father.Name)
	}

	if pokemonData == nil || pokemonData.Abilities.H == "" {
		return getNormalAbility(father, mother) // 如果没有隐藏特性，返回普通特性
	}
	return pokemonData.Abilities.H
}

func calculateSteppedIVLevel(ivsRate float32) int32 {
	if ivsRate <= 0 {
		return 0
	}

	// 限制最大概率为50%
	if ivsRate > 50 {
		ivsRate = 50
	}

	// 计算生效概率：0.5 * ivsRate * 0.01，最高不超过50%
	effectProb := 0.5 * ivsRate * 0.01
	if effectProb > 0.5 {
		effectProb = 0.5
	}

	// 判断是否生效
	if rand.Float32() >= effectProb {
		return 0 // 不生效，返回0v
	}

	// 确定阶梯等级
	var maxVLevel int32
	switch {
	case ivsRate >= 0 && ivsRate < 20:
		maxVLevel = 2
	case ivsRate >= 20 && ivsRate < 40:
		maxVLevel = 3
	case ivsRate >= 40 && ivsRate < 60:
		maxVLevel = 4
	case ivsRate >= 60 && ivsRate < 80:
		maxVLevel = 5
	case ivsRate >= 80:
		maxVLevel = 6
	default:
		maxVLevel = 0
	}

	// 计算产出对应V数的概率：ivsRate%
	vProb := ivsRate / 100.0
	if vProb > 1.0 {
		vProb = 1.0
	}

	// 从maxVLevel开始向下尝试
	for v := maxVLevel; v >= 1; v-- {
		if rand.Float32() < vProb {
			return v
		}
	}

	return 0
}
