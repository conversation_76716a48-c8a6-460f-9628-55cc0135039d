package poke

import (
	"go-nakama-poke/nconst"
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"

	"google.golang.org/protobuf/proto"
)

// // 全局变量存储所有 Pokémon 数据
// var pokemonInfos map[string]PokemonInfo
var pokemonInfos map[string]*MainServer.PSPokemonData

// LoadPokemonInfos 加载 Pokedex 数据到全局变量 pokemonInfos
func LoadPokemonInfos() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/pokedex.bytes")
	if err != nil {
		log.Fatalf("Failed to read pokedex.bytes file: %v", err)
	}
	var pokemonDataMap MainServer.PSPokemonDataMap
	if err := proto.Unmarshal(data, &pokemonDataMap); err != nil {
		log.Fatalf("Failed to parse proto: %v", err)
	}
	pokemonInfos = pokemonDataMap.Data
	// 解析 JSON 数据直接到全局变量
	// if err := json.Unmarshal(data, &pokemonInfos); err != nil {
	// 	log.Fatalf("Failed to parse pokedex.json: %v", err)
	// }

	log.Printf("Successfully loaded %d Pokémon entries into pokemonInfos.", len(pokemonInfos))
}

func GetPokemonInfo(name string) (*MainServer.PSPokemonData, bool) {
	// 检查 name 是否在 map 中
	pokemonData, exists := pokemonInfos[name]
	if !exists {
		return nil, exists
	}
	return pokemonData, true
}
func GetAllPokemonInfo() map[string]*MainServer.PSPokemonData {
	pokemonData := pokemonInfos
	return pokemonData
}
func IsHiddenAbility(ability string, pokemonName string) (bool, error) {
	pokemonData, exists := GetPokemonInfo(pokemonName)
	if !exists {
		return false, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	return ability == pokemonData.Abilities.H, nil
}
func ContainsAbility(ability string, pokemonName string) (bool, error) {
	pokemonData, exists := GetPokemonInfo(pokemonName)
	if !exists {
		return false, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	return ability == pokemonData.Abilities.Key0 || ability == pokemonData.Abilities.Key1 || ability == pokemonData.Abilities.H, nil
}

// GetPokemonTotalityAndLevel 通过 Pokémon 名称获取 Totality 和对应的等级 //用于计算获取到的经验值
// func GetPokemonStatLevel(pokemonName string) int32 {
// 	if pokeInfo, ok := pokemonStats[pokemonName]; ok {
// 		totality := pokeInfo.Totality
// 		level := GetPokemonStatLevelByTotality(totality)
// 		return level
// 	}
// 	return 0 // 如果未找到 Pokémon，则返回默认值
// }

// GetPokemonLevelByTotality 根据 Totality 返回等级
func GetPokemonStatLevelByTotality(totality int32) int32 {
	switch {
	case totality > 0 && totality < 350:
		return 1
	case totality >= 350 && totality < 420:
		return 2
	case totality >= 420 && totality < 490:
		return 3
	case totality >= 490 && totality < 560:
		return 4
	case totality >= 560 && totality < 600:
		return 5
	case totality >= 600 && totality < 670:
		return 5
	case totality >= 670:
		return 6
	default:
		return 0 // 表示无效等级
	}
}

// CalculateMaxHP 计算宝可梦的最大 HP
func CalculateMaxHP(baseStat, iv, ev, level int) int {
	// 校验输入范围
	if baseStat < 1 || iv < 0 || iv > 31 || ev < 0 || ev > 255 || level < 1 || level > 100 {
		return 0 // 如果输入无效，返回 0 表示错误
	}

	// 计算公式
	maxHP := ((2*baseStat + iv + ev/4) * level / 100) + level + 10
	return maxHP
}
