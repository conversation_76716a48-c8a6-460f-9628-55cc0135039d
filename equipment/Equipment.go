package equipment

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/proto/MainServer"
	"math"
	"math/rand"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TableEquipmentName = "equipment"
)

// 初始化装备表
func InitEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createEquipmentTableIfNotExists(ctx, logger, db)
	// addIsDeleteColumnIfNotExists(ctx, logger, db)
	LoadEquipmentConfigInfos()
}

// 添加is_delete字段（如果不存在）
// func addIsDeleteColumnIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
// 	// 检查is_delete字段是否存在
// 	checkQuery := fmt.Sprintf(`
// 		SELECT column_name
// 		FROM information_schema.columns
// 		WHERE table_name = '%s' AND column_name = 'is_delete'
// 	`, TableEquipmentName)

// 	var columnName string
// 	err := db.QueryRowContext(ctx, checkQuery).Scan(&columnName)
// 	if err == nil {
// 		// 字段已存在
// 		logger.Info("is_delete字段已存在，跳过添加")
// 		return
// 	}

// 	// 添加is_delete字段
// 	alterQuery := fmt.Sprintf(`
// 		ALTER TABLE %s ADD COLUMN is_delete BOOLEAN NOT NULL DEFAULT FALSE
// 	`, TableEquipmentName)

// 	_, err = db.ExecContext(ctx, alterQuery)
// 	if err != nil {
// 		logger.Error("添加is_delete字段失败: %v", err)
// 		return
// 	}

// 	// 添加索引
// 	indexQuery := fmt.Sprintf(`
// 		CREATE INDEX IF NOT EXISTS idx_equipment_is_delete ON %s (is_delete)
// 	`, TableEquipmentName)

// 	_, err = db.ExecContext(ctx, indexQuery)
// 	if err != nil {
// 		logger.Error("创建is_delete索引失败: %v", err)
// 		return
// 	}

// 	logger.Info("成功添加is_delete字段和索引")
// }

// 创建装备表（如果不存在）
func createEquipmentTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            tid BIGINT NOT NULL,
            equipment_name VARCHAR(50) NOT NULL,
            expired_ts BIGINT NOT NULL DEFAULT 0,
            price INT NOT NULL DEFAULT 0,
            team_coin INT NOT NULL DEFAULT 0,
            team_type INT NOT NULL DEFAULT 0,
            status INT NOT NULL DEFAULT 1,
            equipment_type INT NOT NULL DEFAULT 0,
            item_sale_type INT NOT NULL DEFAULT 0,
            fortify_count INT NOT NULL DEFAULT 0,
            effect_info JSONB NOT NULL DEFAULT '{}',
            is_delete BOOLEAN NOT NULL DEFAULT FALSE,
            create_ts BIGINT NOT NULL,
            update_ts BIGINT NOT NULL
        );
        CREATE INDEX IF NOT EXISTS idx_equipment_id ON %s (id);
        CREATE INDEX IF NOT EXISTS idx_equipment_tid ON %s (tid);
        CREATE INDEX IF NOT EXISTS idx_equipment_equipment_name ON %s (equipment_name);
        CREATE INDEX IF NOT EXISTS idx_equipment_expired_ts ON %s (expired_ts);
        CREATE INDEX IF NOT EXISTS idx_equipment_status ON %s (status);
        CREATE INDEX IF NOT EXISTS idx_equipment_equipment_type ON %s (equipment_type);
        CREATE INDEX IF NOT EXISTS idx_equipment_item_sale_type ON %s (item_sale_type);
        CREATE INDEX IF NOT EXISTS idx_equipment_fortify_count ON %s (fortify_count);
        CREATE INDEX IF NOT EXISTS idx_equipment_is_delete ON %s (is_delete);
        CREATE INDEX IF NOT EXISTS idx_equipment_update_ts ON %s (update_ts);
    `, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("创建装备表失败: %v", err)
		return
	}

	logger.Info("装备表初始化完成")
}

// 添加装备
func AddEquipment(ctx context.Context, tx *sql.Tx, equipment *MainServer.Equipment) error {
	// 序列化效果信息
	effectInfoBytes, err := json.Marshal(equipment.EffectInfo)
	if err != nil {
		return fmt.Errorf("序列化效果信息失败: %w", err)
	}

	now := time.Now().Unix()
	if equipment.CreateTs == 0 {
		equipment.CreateTs = now
	}
	equipment.UpdateTs = now
	// 确保新装备默认不是删除状态
	equipment.IsDelete = false

	query := fmt.Sprintf(`
		INSERT INTO %s (tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, is_delete, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		RETURNING id
	`, TableEquipmentName)

	err = tx.QueryRowContext(ctx, query,
		equipment.Tid,
		equipment.EquipmentName,
		equipment.ExpiredTs,
		equipment.Price,
		equipment.TeamCoin,
		int(equipment.TeamType),
		int(equipment.Status),
		int(equipment.EquipmentType),
		int(equipment.ItemSaleType),
		equipment.FortifyCount,
		string(effectInfoBytes),
		equipment.IsDelete,
		equipment.CreateTs,
		equipment.UpdateTs,
	).Scan(&equipment.Id)

	if err != nil {
		return fmt.Errorf("添加装备失败: %w", err)
	}

	return nil
}

// 更新装备
func UpdateEquipment(ctx context.Context, tx *sql.Tx, equipment *MainServer.Equipment) error {
	// 序列化效果信息
	effectInfoBytes, err := json.Marshal(equipment.EffectInfo)
	if err != nil {
		return fmt.Errorf("序列化效果信息失败: %w", err)
	}

	equipment.UpdateTs = time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET
			equipment_name = $2,
			expired_ts = $3,
			price = $4,
			team_coin = $5,
			team_type = $6,
			status = $7,
			equipment_type = $8,
			item_sale_type = $9,
			fortify_count = $10,
			effect_info = $11,
			is_delete = $12,
			update_ts = $13
		WHERE id = $1
	`, TableEquipmentName)

	result, err := tx.ExecContext(ctx, query,
		equipment.Id,
		equipment.EquipmentName,
		equipment.ExpiredTs,
		equipment.Price,
		equipment.TeamCoin,
		int(equipment.TeamType),
		int(equipment.Status),
		int(equipment.EquipmentType),
		int(equipment.ItemSaleType),
		equipment.FortifyCount,
		string(effectInfoBytes),
		equipment.IsDelete,
		equipment.UpdateTs,
	)

	if err != nil {
		return fmt.Errorf("更新装备失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("装备不存在")
	}

	return nil
}

// 删除装备（软删除）
func DeleteEquipment(ctx context.Context, tx *sql.Tx, equipmentId int64) error {
	now := time.Now().Unix()
	query := fmt.Sprintf(`
		UPDATE %s SET
			is_delete = TRUE,
			update_ts = $2
		WHERE id = $1 AND is_delete = FALSE
	`, TableEquipmentName)

	result, err := tx.ExecContext(ctx, query, equipmentId, now)
	if err != nil {
		return fmt.Errorf("删除装备失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("装备不存在或已被删除")
	}

	return nil
}

// 查询装备
func QueryEquipments(ctx context.Context, tx *sql.Tx, filter *MainServer.EquipmentFilter) ([]*MainServer.Equipment, error) {
	// 构建查询条件
	conditions := []string{}
	args := []interface{}{}
	paramIndex := 1

	// 默认过滤已删除的装备
	conditions = append(conditions, fmt.Sprintf("is_delete = $%d", paramIndex))
	args = append(args, false)
	paramIndex++

	// 如果指定了 id，则只查询该 id
	if filter.Id > 0 {
		conditions = append(conditions, fmt.Sprintf("id = $%d", paramIndex))
		args = append(args, filter.Id)
		paramIndex++
	}

	// tid 是必填的（除非指定了id）
	if filter.Tid > 0 {
		conditions = append(conditions, fmt.Sprintf("tid = $%d", paramIndex))
		args = append(args, filter.Tid)
		paramIndex++
	}
	if filter.EquipmentType != 0 {
		conditions = append(conditions, fmt.Sprintf("equipment_type = $%d", paramIndex))
		args = append(args, int(filter.EquipmentType))
		paramIndex++
	}
	if filter.EquipmentName != "" {
		conditions = append(conditions, fmt.Sprintf("equipment_name = $%d", paramIndex))
		args = append(args, filter.EquipmentName)
		paramIndex++
	}

	if filter.Status != 0 {
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(filter.Status))
		paramIndex++
	}

	if filter.FortifyCount > 0 {
		conditions = append(conditions, fmt.Sprintf("fortify_count = $%d", paramIndex))
		args = append(args, filter.FortifyCount)
		paramIndex++
	}

	if filter.UpdateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, filter.UpdateTs)
		paramIndex++
	}

	// TODO: 等proto更新后启用这些过滤条件
	// if filter.ExpiredTs > 0 {
	// 	conditions = append(conditions, fmt.Sprintf("expired_ts = $%d", paramIndex))
	// 	args = append(args, filter.ExpiredTs)
	// 	paramIndex++
	// }

	if filter.ItemSaleType != 0 {
		conditions = append(conditions, fmt.Sprintf("item_sale_type = $%d", paramIndex))
		args = append(args, int(filter.ItemSaleType))
		paramIndex++
	}

	// 过期装备过滤（默认不包含已过期的装备）
	// if !filter.IncludeExpired {
	// 	now := time.Now().Unix()
	// 	conditions = append(conditions, fmt.Sprintf("(expired_ts = 0 OR expired_ts > $%d)", paramIndex))
	// 	args = append(args, now)
	// 	paramIndex++
	// }

	// 构建查询语句（conditions已经包含is_delete = false，所以不会为空）
	query := fmt.Sprintf(`
		SELECT id, tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, is_delete, create_ts, update_ts
		FROM %s
		WHERE %s
		ORDER BY update_ts DESC
	`, TableEquipmentName, strings.Join(conditions, " AND "))

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询装备失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var equipments []*MainServer.Equipment
	for rows.Next() {
		equipment := &MainServer.Equipment{}
		var statusInt, equipmentTypeInt, teamTypeInt, itemSaleTypeInt int
		var effectInfoStr string

		err := rows.Scan(
			&equipment.Id,
			&equipment.Tid,
			&equipment.EquipmentName,
			&equipment.ExpiredTs,
			&equipment.Price,
			&equipment.TeamCoin,
			&teamTypeInt,
			&statusInt,
			&equipmentTypeInt,
			&itemSaleTypeInt,
			&equipment.FortifyCount,
			&effectInfoStr,
			&equipment.IsDelete,
			&equipment.CreateTs,
			&equipment.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描装备数据失败: %w", err)
		}

		equipment.Status = MainServer.EquipmentStatus(statusInt)
		equipment.EquipmentType = MainServer.EquipmentType(equipmentTypeInt)
		equipment.TeamType = MainServer.TrainerTeam(teamTypeInt)
		equipment.ItemSaleType = MainServer.ItemSaleType(itemSaleTypeInt)

		// 反序列化效果信息
		equipment.EffectInfo = &MainServer.TrainerEquipmentEffect{}
		if effectInfoStr != "" && effectInfoStr != "{}" {
			err = json.Unmarshal([]byte(effectInfoStr), equipment.EffectInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化效果信息失败: %w", err)
			}
		}

		equipments = append(equipments, equipment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历装备数据失败: %w", err)
	}

	return equipments, nil
}

// 获取单个装备
func GetEquipmentById(ctx context.Context, tx *sql.Tx, equipmentId int64) (*MainServer.Equipment, error) {
	filter := &MainServer.EquipmentFilter{
		Id: equipmentId,
	}

	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(equipments) == 0 {
		return nil, fmt.Errorf("装备不存在")
	}

	return equipments[0], nil
}

// 获取训练师的所有装备
func GetTrainerEquipments(ctx context.Context, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.Equipment, error) {
	filter := &MainServer.EquipmentFilter{
		Tid:      tid,
		UpdateTs: updateTs,
	}

	return QueryEquipments(ctx, tx, filter)
}

// 上架装备
func SaleEquipment(ctx context.Context, tx *sql.Tx, tid int64, param *MainServer.SaleEquipmentParam) error {
	// 查找装备
	filter := &MainServer.EquipmentFilter{
		Tid:           tid,
		EquipmentName: param.EquipmentName,
		Status:        MainServer.EquipmentStatus_EquipmentStatus_Normal,
	}

	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return err
	}

	if len(equipments) == 0 {
		return runtime.NewError("未找到装备", 400)
	}

	equipment := equipments[0]
	if equipment.ItemSaleType == MainServer.ItemSaleType_ItemSaleType_Trainer_Only && equipment.Tid != tid {
		return runtime.NewError("专属装备不能上架", 400)
	}

	// 更新装备状态和价格
	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Sale
	equipment.Price = param.Price
	equipment.TeamCoin = param.SpecialCoin

	return UpdateEquipment(ctx, tx, equipment)
}

// 下架装备
func UnsaleEquipment(ctx context.Context, tx *sql.Tx, tid int64, param *MainServer.UnsaleEquipmentParam) error {
	// 查找装备
	filter := &MainServer.EquipmentFilter{
		Tid:           tid,
		EquipmentName: param.EquipmentName,
		Status:        MainServer.EquipmentStatus_EquipmentStatus_Sale,
	}

	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return err
	}

	if len(equipments) == 0 {
		return fmt.Errorf("装备不存在")
	}

	equipment := equipments[0]

	// 更新装备状态
	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Normal
	equipment.Price = 0
	equipment.TeamCoin = 0

	return UpdateEquipment(ctx, tx, equipment)
}

// 获取所有装备（管理员用）- 包括已删除的装备
func GetAllEquipments(ctx context.Context, tx *sql.Tx, updateTs int64) ([]*MainServer.Equipment, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, is_delete, create_ts, update_ts
		FROM %s
		WHERE update_ts > $1
		ORDER BY update_ts DESC
	`, TableEquipmentName)

	rows, err := tx.QueryContext(ctx, query, updateTs)
	if err != nil {
		return nil, fmt.Errorf("查询装备失败: %w", err)
	}
	defer rows.Close()

	var equipments []*MainServer.Equipment
	for rows.Next() {
		equipment := &MainServer.Equipment{}
		var statusInt, equipmentTypeInt, teamTypeInt, itemSaleTypeInt int
		var effectInfoStr string

		err := rows.Scan(
			&equipment.Id,
			&equipment.Tid,
			&equipment.EquipmentName,
			&equipment.ExpiredTs,
			&equipment.Price,
			&equipment.TeamCoin,
			&teamTypeInt,
			&statusInt,
			&equipmentTypeInt,
			&itemSaleTypeInt,
			&equipment.FortifyCount,
			&effectInfoStr,
			&equipment.IsDelete,
			&equipment.CreateTs,
			&equipment.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描装备数据失败: %w", err)
		}

		equipment.Status = MainServer.EquipmentStatus(statusInt)
		equipment.EquipmentType = MainServer.EquipmentType(equipmentTypeInt)
		equipment.TeamType = MainServer.TrainerTeam(teamTypeInt)
		equipment.ItemSaleType = MainServer.ItemSaleType(itemSaleTypeInt)

		// 反序列化效果信息
		equipment.EffectInfo = &MainServer.TrainerEquipmentEffect{}
		if effectInfoStr != "" && effectInfoStr != "{}" {
			err = json.Unmarshal([]byte(effectInfoStr), equipment.EffectInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化效果信息失败: %w", err)
			}
		}

		equipments = append(equipments, equipment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历装备数据失败: %w", err)
	}

	return equipments, nil
}

// 查询装备（包括已删除的装备）- 管理员专用
func QueryAllEquipmentsIncludeDeleted(ctx context.Context, tx *sql.Tx, filter *MainServer.EquipmentFilter) ([]*MainServer.Equipment, error) {
	// 构建查询条件
	conditions := []string{}
	args := []interface{}{}
	paramIndex := 1

	// 如果指定了 id，则只查询该 id
	if filter.Id > 0 {
		conditions = append(conditions, fmt.Sprintf("id = $%d", paramIndex))
		args = append(args, filter.Id)
		paramIndex++
	}

	// tid 是必填的（除非指定了id）
	if filter.Tid > 0 {
		conditions = append(conditions, fmt.Sprintf("tid = $%d", paramIndex))
		args = append(args, filter.Tid)
		paramIndex++
	}
	if filter.EquipmentType != 0 {
		conditions = append(conditions, fmt.Sprintf("equipment_type = $%d", paramIndex))
		args = append(args, int(filter.EquipmentType))
		paramIndex++
	}
	if filter.EquipmentName != "" {
		conditions = append(conditions, fmt.Sprintf("equipment_name = $%d", paramIndex))
		args = append(args, filter.EquipmentName)
		paramIndex++
	}

	if filter.Status != 0 {
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(filter.Status))
		paramIndex++
	}

	if filter.FortifyCount > 0 {
		conditions = append(conditions, fmt.Sprintf("fortify_count = $%d", paramIndex))
		args = append(args, filter.FortifyCount)
		paramIndex++
	}

	if filter.UpdateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, filter.UpdateTs)
		paramIndex++
	}

	if filter.ItemSaleType != 0 {
		conditions = append(conditions, fmt.Sprintf("item_sale_type = $%d", paramIndex))
		args = append(args, int(filter.ItemSaleType))
		paramIndex++
	}

	// 如果没有任何条件，返回错误
	if len(conditions) == 0 {
		return nil, fmt.Errorf("查询条件不能为空")
	}

	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, is_delete, create_ts, update_ts
		FROM %s
		WHERE %s
		ORDER BY update_ts DESC
	`, TableEquipmentName, strings.Join(conditions, " AND "))

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询装备失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var equipments []*MainServer.Equipment
	for rows.Next() {
		equipment := &MainServer.Equipment{}
		var statusInt, equipmentTypeInt, teamTypeInt, itemSaleTypeInt int
		var effectInfoStr string

		err := rows.Scan(
			&equipment.Id,
			&equipment.Tid,
			&equipment.EquipmentName,
			&equipment.ExpiredTs,
			&equipment.Price,
			&equipment.TeamCoin,
			&teamTypeInt,
			&statusInt,
			&equipmentTypeInt,
			&itemSaleTypeInt,
			&equipment.FortifyCount,
			&effectInfoStr,
			&equipment.IsDelete,
			&equipment.CreateTs,
			&equipment.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描装备数据失败: %w", err)
		}

		equipment.Status = MainServer.EquipmentStatus(statusInt)
		equipment.EquipmentType = MainServer.EquipmentType(equipmentTypeInt)
		equipment.TeamType = MainServer.TrainerTeam(teamTypeInt)
		equipment.ItemSaleType = MainServer.ItemSaleType(itemSaleTypeInt)

		// 反序列化效果信息
		equipment.EffectInfo = &MainServer.TrainerEquipmentEffect{}
		if effectInfoStr != "" && effectInfoStr != "{}" {
			err = json.Unmarshal([]byte(effectInfoStr), equipment.EffectInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化效果信息失败: %w", err)
			}
		}

		equipments = append(equipments, equipment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历装备数据失败: %w", err)
	}

	return equipments, nil
}

// 恢复已删除的装备
func RestoreEquipment(ctx context.Context, tx *sql.Tx, equipmentId int64) error {
	now := time.Now().Unix()
	query := fmt.Sprintf(`
		UPDATE %s SET
			is_delete = FALSE,
			update_ts = $2
		WHERE id = $1 AND is_delete = TRUE
	`, TableEquipmentName)

	result, err := tx.ExecContext(ctx, query, equipmentId, now)
	if err != nil {
		return fmt.Errorf("恢复装备失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("装备不存在或未被删除")
	}

	return nil
}

// 装备到TrainerDecoration
func EquipToTrainerDecoration(ctx context.Context, tx *sql.Tx, tid int64, equipmentId int64, equipmentType MainServer.EquipmentType) (*MainServer.TrainerEquipmentInfo, error) {
	// 获取装备信息
	equipment, err := GetEquipmentById(ctx, tx, equipmentId)
	if err != nil {
		return nil, err
	}

	// 检查装备是否属于该训练师
	if equipment.Tid != tid {
		return nil, fmt.Errorf("装备不属于该训练师")
	}

	// 检查装备状态
	if equipment.Status != MainServer.EquipmentStatus_EquipmentStatus_Normal {
		return nil, fmt.Errorf("装备状态不正常，无法装备")
	}

	// 检查装备类型是否匹配
	if equipment.EquipmentType != equipmentType {
		return nil, fmt.Errorf("装备类型不匹配")
	}

	// 检查装备是否过期
	if IsEquipmentExpired(equipment) {
		return nil, fmt.Errorf("装备已过期，无法装备")
	}

	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Equip
	err = UpdateEquipment(ctx, tx, equipment)
	if err != nil {
		return nil, err
	}

	// 创建TrainerEquipmentInfo
	equipmentInfo := &MainServer.TrainerEquipmentInfo{
		Id:            equipment.Id,
		EquipmentName: equipment.EquipmentName,
		EquipmentType: equipment.EquipmentType,
		FortifyCount:  equipment.FortifyCount,
		EffectInfo:    equipment.EffectInfo,
	}

	return equipmentInfo, nil
}

// 卸下装备
func UnequipFromTrainerDecoration(ctx context.Context, tx *sql.Tx, tid int64, equipmentId int64) error {
	// 检查装备是否存在且属于该训练师
	equipment, err := GetEquipmentById(ctx, tx, equipmentId)
	if err != nil {
		return err
	}

	if equipment.Tid != tid {
		return fmt.Errorf("装备不属于该训练师")
	}
	if equipment.Status != MainServer.EquipmentStatus_EquipmentStatus_Equip {
		return fmt.Errorf("装备未装备，无需卸下")
	}
	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Normal
	// 装备卸下后不需要特殊处理，只要确保装备存在即可
	return UpdateEquipment(ctx, tx, equipment)
}

type FortifyEquipmentResult struct {
	EquipmentFortifyErrorCode MainServer.EquipmentFortifyErrorCode
	IsEquiped                 bool
	EquipmentType             MainServer.EquipmentType
}

// 强化装备
func FortifyEquipment(ctx context.Context, tx *sql.Tx, tid int64, equipmentId int64, subEquipmentId int64, param *MainServer.EquipmentFortifyParam) (FortifyEquipmentResult, error) {
	result := FortifyEquipmentResult{}
	// 检查装备是否属于该训练师
	filter := &MainServer.EquipmentFilter{
		Id:  equipmentId,
		Tid: tid,
	}
	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_EquipmentNotFind
		return result, runtime.NewError(fmt.Sprintf("Failed to query equipment: %v", err), 500)
	}
	if len(equipments) == 0 {
		result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_EquipmentNotFind
		return result, runtime.NewError("装备不存在或不属于该训练师", 404)
	}
	mainEquipment := equipments[0]
	localConfig, exists := getLocalEquipmentConfig(mainEquipment.EquipmentName)
	if !exists {
		result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_EquipmentNotFind
		return result, fmt.Errorf("装备配置不存在")
	}
	result.EquipmentType = mainEquipment.EquipmentType
	isEquiped := false
	if mainEquipment.Status == MainServer.EquipmentStatus_EquipmentStatus_Equip {
		isEquiped = true
	}
	if subEquipmentId > 0 {
		filter.Id = subEquipmentId
		subEquipments, err := QueryEquipments(ctx, tx, filter)
		if err != nil {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_EquipmentNotFind
			return result, err
		}
		if len(subEquipments) == 0 {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_EquipmentNotFind
			return result, runtime.NewError("装备不存在或不属于该训练师", 404)
		}
		subEquipment := subEquipments[0]
		if mainEquipment.EquipmentName != subEquipment.EquipmentName {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_Fail
			return result, runtime.NewError("装备类型不匹配", 400)
		}
		if subEquipment.Status == MainServer.EquipmentStatus_EquipmentStatus_Equip {
			isEquiped = true
		}
		err = DeleteEquipment(ctx, tx, subEquipment.Id)
		if err != nil {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_Fail
			return result, err
		}
	}
	result.IsEquiped = isEquiped
	// if isEquiped {
	// 	err = UnequipFromTrainerDecoration(ctx, logger, tx, trainer, mainEquipment.EquipmentType)
	// 	if err != nil {
	// 		return "", runtime.NewError(fmt.Sprintf("Failed to unequip from decoration: %v", err), 500)
	// 	}
	// }
	// equipment, err := GetEquipmentById(ctx, tx, equipmentId)
	// if err != nil {
	// 	return MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_EquipmentNotFind, err
	// }
	// param.MaterialPokes
	materialPokeInfos := make(map[string]*MainServer.Poke)
	materialItemInfos := make(map[string]*MainServer.FortifyMaterial)
	materialEuipmentInfos := make(map[string]*MainServer.Poke)

	//需要消耗localConfig中设置的素材
	for _, v := range localConfig.FortifyMaterials {
		switch v.MaterialType {
		case MainServer.FortifyMaterialType_FortifyMaterialType_Item:
			if v.MaterialMinLevel > mainEquipment.FortifyCount+1 || v.MaterialMaxLevel < mainEquipment.FortifyCount+1 {
				continue
			}
			_, ex := item.GetItemByName(v.MaterialName)
			if !ex {
				result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_MaterialNotFind
				return result, fmt.Errorf("failed to read item: %s", v.MaterialName)
			}
			materialItemInfos[v.MaterialName] = v
			break
		case MainServer.FortifyMaterialType_FortifyMaterialType_Euipment:
			//TODO 后续
			materialEuipmentInfos[v.MaterialName] = nil
			break
		case MainServer.FortifyMaterialType_FortifyMaterialType_Poke:
			//TODO 后续
			materialPokeInfos[v.MaterialName] = nil
			break
		}
	}
	for _, v := range param.MaterialPokes {
		_, exists = materialPokeInfos[v.Name]
		if !exists {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_MaterialNotEnough
			return result, fmt.Errorf("poke not in material: %s", v.Name)
		}
	}
	for _, v := range param.MaterialEquipments {
		_, exists = materialEuipmentInfos[v.Name]
		if !exists {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_MaterialNotEnough
			return result, fmt.Errorf("equipment not in material: %s", v.Name)
		}
	}
	for _, v := range materialItemInfos {
		_, err = inventory.RemoveNormalItemByItemName(ctx, tx, tid, v.MaterialName, v.MaterialCount)
		if err != nil {
			result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_MaterialNotEnough
			return result, err
		}
	}
	//每上升一级成功率降低10%
	successRate := calculateSuccessRate(mainEquipment.FortifyCount)
	if successRate < 0.1 {
		successRate = 0.1
	}
	if rand.Float64() > successRate {
		result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_Success_ButFortifyFail
		return result, nil
	}
	// 更新强化等级
	mainEquipment.FortifyCount++
	err = UpdateEquipment(ctx, tx, mainEquipment)
	if err != nil {
		result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_Fail
		return result, err
	}
	result.EquipmentFortifyErrorCode = MainServer.EquipmentFortifyErrorCode_EquipmentFortifyErrorCode_Success
	return result, nil
}
func calculateSuccessRate(fortifyCount int32) float64 {
	baseRate := 1.0 - 0.1
	exponent := float64(fortifyCount + 1)
	return 1 * math.Pow(baseRate, exponent)
}

// 批量添加装备
func BatchAddEquipments(ctx context.Context, tx *sql.Tx, equipments []*MainServer.Equipment) error {
	for _, equipment := range equipments {
		err := AddEquipment(ctx, tx, equipment)
		if err != nil {
			return fmt.Errorf("批量添加装备失败，装备: %s, 错误: %w", equipment.EquipmentName, err)
		}
	}
	return nil
}

// 检查装备是否过期
func IsEquipmentExpired(equipment *MainServer.Equipment) bool {
	if equipment.ExpiredTs == 0 {
		return false // 永不过期
	}
	return time.Now().Unix() > equipment.ExpiredTs
}

// 获取未过期的装备
func GetValidEquipments(ctx context.Context, tx *sql.Tx, filter *MainServer.EquipmentFilter) ([]*MainServer.Equipment, error) {
	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	var validEquipments []*MainServer.Equipment
	for _, equipment := range equipments {
		if !IsEquipmentExpired(equipment) {
			validEquipments = append(validEquipments, equipment)
		}
	}

	return validEquipments, nil
}
