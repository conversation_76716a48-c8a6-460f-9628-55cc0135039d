// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ErrorCode.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorCode int32

const (
	ErrorCode_ErrorCode_Success ErrorCode = 0
	// 1000-1999: 致命错误 - 客户端应该退出到登录界面
	ErrorCode_ErrorCode_SessionExpired     ErrorCode = 1001 // 会话过期
	ErrorCode_ErrorCode_TrainerNotFound    ErrorCode = 1002 // 训练师未找到（内存中不存在）
	ErrorCode_ErrorCode_AccountBanned      ErrorCode = 1003 // 账号被封禁
	ErrorCode_ErrorCode_ServerMaintenance  ErrorCode = 1004 // 服务器维护
	ErrorCode_ErrorCode_VersionMismatch    ErrorCode = 1005 // 版本不匹配
	ErrorCode_ErrorCode_DuplicateLogin     ErrorCode = 1006 // 重复登录
	ErrorCode_ErrorCode_AuthenticationFail ErrorCode = 1007 // 认证失败
	// 2000-2999: 权限错误 - 客户端显示错误信息
	ErrorCode_ErrorCode_PermissionDenied     ErrorCode = 2001 // 权限不足
	ErrorCode_ErrorCode_AdminRequired        ErrorCode = 2002 // 需要管理员权限
	ErrorCode_ErrorCode_SpecialRightRequired ErrorCode = 2003 // 需要特殊权限
	ErrorCode_ErrorCode_TeamLeaderRequired   ErrorCode = 2004 // 需要队长权限
	ErrorCode_ErrorCode_BoxAccessDenied      ErrorCode = 2005 // 盒子访问被拒绝
	// 3000-3999: 参数错误 - 客户端显示错误信息
	ErrorCode_ErrorCode_InvalidPayload      ErrorCode = 3001 // 无效的请求数据
	ErrorCode_ErrorCode_InvalidParameter    ErrorCode = 3002 // 无效的参数
	ErrorCode_ErrorCode_MissingParameter    ErrorCode = 3003 // 缺少必要参数
	ErrorCode_ErrorCode_ParameterOutOfRange ErrorCode = 3004 // 参数超出范围
	// 4000-4999: 业务逻辑错误 - 客户端显示错误信息
	ErrorCode_ErrorCode_ResourceNotFound    ErrorCode = 4001 // 资源未找到
	ErrorCode_ErrorCode_ResourceExhausted   ErrorCode = 4002 // 资源耗尽
	ErrorCode_ErrorCode_OperationNotAllowed ErrorCode = 4003 // 操作不被允许
	ErrorCode_ErrorCode_InsufficientFunds   ErrorCode = 4004 // 资金不足
	ErrorCode_ErrorCode_InventoryFull       ErrorCode = 4005 // 背包已满
	ErrorCode_ErrorCode_PokemonNotFound     ErrorCode = 4006 // 精灵未找到
	ErrorCode_ErrorCode_ItemNotFound        ErrorCode = 4007 // 道具未找到
	ErrorCode_ErrorCode_BattleNotFound      ErrorCode = 4008 // 战斗未找到
	ErrorCode_ErrorCode_MatchNotFound       ErrorCode = 4009 // 匹配未找到
	ErrorCode_ErrorCode_QuestNotFound       ErrorCode = 4010 // 任务未找到
	ErrorCode_ErrorCode_EmailNotFound       ErrorCode = 4011 // 邮件未找到
	ErrorCode_ErrorCode_TradeNotFound       ErrorCode = 4012 // 交易未找到
	ErrorCode_ErrorCode_CooldownActive      ErrorCode = 4013 // 冷却时间中
	ErrorCode_ErrorCode_RateLimitExceeded   ErrorCode = 4014 // 频率限制超出
	ErrorCode_ErrorCode_OperationLocked     ErrorCode = 4015 // 操作被锁定
	ErrorCode_ErrorCode_InvalidState        ErrorCode = 4016 // 无效状态
	ErrorCode_ErrorCode_NotAnEgg            ErrorCode = 4017 // 不是蛋
	ErrorCode_ErrorCode_ItemCannotUseHere   ErrorCode = 4018 // 道具无法在此场景使用
	ErrorCode_ErrorCode_ConfigNotFound      ErrorCode = 4019 // 配置未找到
	ErrorCode_ErrorCode_PokemonNotInBox     ErrorCode = 4020 // 宝可梦不在盒子中
	ErrorCode_ErrorCode_CannotBreed         ErrorCode = 4021 // 这对宝可梦不能繁殖
	// 5000-5999: 系统错误 - 客户端显示通用错误信息
	ErrorCode_ErrorCode_DatabaseError      ErrorCode = 5001 // 数据库错误
	ErrorCode_ErrorCode_TransactionFailed  ErrorCode = 5002 // 事务失败
	ErrorCode_ErrorCode_SerializationError ErrorCode = 5003 // 序列化错误
	ErrorCode_ErrorCode_NetworkError       ErrorCode = 5004 // 网络错误
	ErrorCode_ErrorCode_InternalError      ErrorCode = 5005 // 内部错误
	ErrorCode_ErrorCode_ServiceUnavailable ErrorCode = 5006 // 服务不可用
	ErrorCode_ErrorCode_Timeout            ErrorCode = 5007 // 超时
	ErrorCode_ErrorCode_ConfigError        ErrorCode = 5008 // 配置错误
	// 6000-6999: 战斗相关错误
	ErrorCode_ErrorCode_BattleServerNotFound     ErrorCode = 6001 // 战斗服务器未找到
	ErrorCode_ErrorCode_BattleInfoError          ErrorCode = 6002 // 战斗信息错误
	ErrorCode_ErrorCode_CreateBattleError        ErrorCode = 6003 // 创建战斗错误
	ErrorCode_ErrorCode_BattleChoiceLocked       ErrorCode = 6004 // 战斗选择被锁定
	ErrorCode_ErrorCode_BattleChoiceEmpty        ErrorCode = 6005 // 战斗选择为空
	ErrorCode_ErrorCode_BattleChoiceTitemInvalid ErrorCode = 6006 // 战斗选择的道具无效
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0:    "ErrorCode_Success",
		1001: "ErrorCode_SessionExpired",
		1002: "ErrorCode_TrainerNotFound",
		1003: "ErrorCode_AccountBanned",
		1004: "ErrorCode_ServerMaintenance",
		1005: "ErrorCode_VersionMismatch",
		1006: "ErrorCode_DuplicateLogin",
		1007: "ErrorCode_AuthenticationFail",
		2001: "ErrorCode_PermissionDenied",
		2002: "ErrorCode_AdminRequired",
		2003: "ErrorCode_SpecialRightRequired",
		2004: "ErrorCode_TeamLeaderRequired",
		2005: "ErrorCode_BoxAccessDenied",
		3001: "ErrorCode_InvalidPayload",
		3002: "ErrorCode_InvalidParameter",
		3003: "ErrorCode_MissingParameter",
		3004: "ErrorCode_ParameterOutOfRange",
		4001: "ErrorCode_ResourceNotFound",
		4002: "ErrorCode_ResourceExhausted",
		4003: "ErrorCode_OperationNotAllowed",
		4004: "ErrorCode_InsufficientFunds",
		4005: "ErrorCode_InventoryFull",
		4006: "ErrorCode_PokemonNotFound",
		4007: "ErrorCode_ItemNotFound",
		4008: "ErrorCode_BattleNotFound",
		4009: "ErrorCode_MatchNotFound",
		4010: "ErrorCode_QuestNotFound",
		4011: "ErrorCode_EmailNotFound",
		4012: "ErrorCode_TradeNotFound",
		4013: "ErrorCode_CooldownActive",
		4014: "ErrorCode_RateLimitExceeded",
		4015: "ErrorCode_OperationLocked",
		4016: "ErrorCode_InvalidState",
		4017: "ErrorCode_NotAnEgg",
		4018: "ErrorCode_ItemCannotUseHere",
		4019: "ErrorCode_ConfigNotFound",
		4020: "ErrorCode_PokemonNotInBox",
		4021: "ErrorCode_CannotBreed",
		5001: "ErrorCode_DatabaseError",
		5002: "ErrorCode_TransactionFailed",
		5003: "ErrorCode_SerializationError",
		5004: "ErrorCode_NetworkError",
		5005: "ErrorCode_InternalError",
		5006: "ErrorCode_ServiceUnavailable",
		5007: "ErrorCode_Timeout",
		5008: "ErrorCode_ConfigError",
		6001: "ErrorCode_BattleServerNotFound",
		6002: "ErrorCode_BattleInfoError",
		6003: "ErrorCode_CreateBattleError",
		6004: "ErrorCode_BattleChoiceLocked",
		6005: "ErrorCode_BattleChoiceEmpty",
		6006: "ErrorCode_BattleChoiceTitemInvalid",
	}
	ErrorCode_value = map[string]int32{
		"ErrorCode_Success":                  0,
		"ErrorCode_SessionExpired":           1001,
		"ErrorCode_TrainerNotFound":          1002,
		"ErrorCode_AccountBanned":            1003,
		"ErrorCode_ServerMaintenance":        1004,
		"ErrorCode_VersionMismatch":          1005,
		"ErrorCode_DuplicateLogin":           1006,
		"ErrorCode_AuthenticationFail":       1007,
		"ErrorCode_PermissionDenied":         2001,
		"ErrorCode_AdminRequired":            2002,
		"ErrorCode_SpecialRightRequired":     2003,
		"ErrorCode_TeamLeaderRequired":       2004,
		"ErrorCode_BoxAccessDenied":          2005,
		"ErrorCode_InvalidPayload":           3001,
		"ErrorCode_InvalidParameter":         3002,
		"ErrorCode_MissingParameter":         3003,
		"ErrorCode_ParameterOutOfRange":      3004,
		"ErrorCode_ResourceNotFound":         4001,
		"ErrorCode_ResourceExhausted":        4002,
		"ErrorCode_OperationNotAllowed":      4003,
		"ErrorCode_InsufficientFunds":        4004,
		"ErrorCode_InventoryFull":            4005,
		"ErrorCode_PokemonNotFound":          4006,
		"ErrorCode_ItemNotFound":             4007,
		"ErrorCode_BattleNotFound":           4008,
		"ErrorCode_MatchNotFound":            4009,
		"ErrorCode_QuestNotFound":            4010,
		"ErrorCode_EmailNotFound":            4011,
		"ErrorCode_TradeNotFound":            4012,
		"ErrorCode_CooldownActive":           4013,
		"ErrorCode_RateLimitExceeded":        4014,
		"ErrorCode_OperationLocked":          4015,
		"ErrorCode_InvalidState":             4016,
		"ErrorCode_NotAnEgg":                 4017,
		"ErrorCode_ItemCannotUseHere":        4018,
		"ErrorCode_ConfigNotFound":           4019,
		"ErrorCode_PokemonNotInBox":          4020,
		"ErrorCode_CannotBreed":              4021,
		"ErrorCode_DatabaseError":            5001,
		"ErrorCode_TransactionFailed":        5002,
		"ErrorCode_SerializationError":       5003,
		"ErrorCode_NetworkError":             5004,
		"ErrorCode_InternalError":            5005,
		"ErrorCode_ServiceUnavailable":       5006,
		"ErrorCode_Timeout":                  5007,
		"ErrorCode_ConfigError":              5008,
		"ErrorCode_BattleServerNotFound":     6001,
		"ErrorCode_BattleInfoError":          6002,
		"ErrorCode_CreateBattleError":        6003,
		"ErrorCode_BattleChoiceLocked":       6004,
		"ErrorCode_BattleChoiceEmpty":        6005,
		"ErrorCode_BattleChoiceTitemInvalid": 6006,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ErrorCode_proto_enumTypes[0].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_MainServer_ErrorCode_proto_enumTypes[0]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ErrorCode_proto_rawDescGZIP(), []int{0}
}

var File_MainServer_ErrorCode_proto protoreflect.FileDescriptor

const file_MainServer_ErrorCode_proto_rawDesc = "" +
	"\n" +
	"\x1aMainServer/ErrorCode.proto\x12\n" +
	"MainServer*\x88\r\n" +
	"\tErrorCode\x12\x15\n" +
	"\x11ErrorCode_Success\x10\x00\x12\x1d\n" +
	"\x18ErrorCode_SessionExpired\x10\xe9\a\x12\x1e\n" +
	"\x19ErrorCode_TrainerNotFound\x10\xea\a\x12\x1c\n" +
	"\x17ErrorCode_AccountBanned\x10\xeb\a\x12 \n" +
	"\x1bErrorCode_ServerMaintenance\x10\xec\a\x12\x1e\n" +
	"\x19ErrorCode_VersionMismatch\x10\xed\a\x12\x1d\n" +
	"\x18ErrorCode_DuplicateLogin\x10\xee\a\x12!\n" +
	"\x1cErrorCode_AuthenticationFail\x10\xef\a\x12\x1f\n" +
	"\x1aErrorCode_PermissionDenied\x10\xd1\x0f\x12\x1c\n" +
	"\x17ErrorCode_AdminRequired\x10\xd2\x0f\x12#\n" +
	"\x1eErrorCode_SpecialRightRequired\x10\xd3\x0f\x12!\n" +
	"\x1cErrorCode_TeamLeaderRequired\x10\xd4\x0f\x12\x1e\n" +
	"\x19ErrorCode_BoxAccessDenied\x10\xd5\x0f\x12\x1d\n" +
	"\x18ErrorCode_InvalidPayload\x10\xb9\x17\x12\x1f\n" +
	"\x1aErrorCode_InvalidParameter\x10\xba\x17\x12\x1f\n" +
	"\x1aErrorCode_MissingParameter\x10\xbb\x17\x12\"\n" +
	"\x1dErrorCode_ParameterOutOfRange\x10\xbc\x17\x12\x1f\n" +
	"\x1aErrorCode_ResourceNotFound\x10\xa1\x1f\x12 \n" +
	"\x1bErrorCode_ResourceExhausted\x10\xa2\x1f\x12\"\n" +
	"\x1dErrorCode_OperationNotAllowed\x10\xa3\x1f\x12 \n" +
	"\x1bErrorCode_InsufficientFunds\x10\xa4\x1f\x12\x1c\n" +
	"\x17ErrorCode_InventoryFull\x10\xa5\x1f\x12\x1e\n" +
	"\x19ErrorCode_PokemonNotFound\x10\xa6\x1f\x12\x1b\n" +
	"\x16ErrorCode_ItemNotFound\x10\xa7\x1f\x12\x1d\n" +
	"\x18ErrorCode_BattleNotFound\x10\xa8\x1f\x12\x1c\n" +
	"\x17ErrorCode_MatchNotFound\x10\xa9\x1f\x12\x1c\n" +
	"\x17ErrorCode_QuestNotFound\x10\xaa\x1f\x12\x1c\n" +
	"\x17ErrorCode_EmailNotFound\x10\xab\x1f\x12\x1c\n" +
	"\x17ErrorCode_TradeNotFound\x10\xac\x1f\x12\x1d\n" +
	"\x18ErrorCode_CooldownActive\x10\xad\x1f\x12 \n" +
	"\x1bErrorCode_RateLimitExceeded\x10\xae\x1f\x12\x1e\n" +
	"\x19ErrorCode_OperationLocked\x10\xaf\x1f\x12\x1b\n" +
	"\x16ErrorCode_InvalidState\x10\xb0\x1f\x12\x17\n" +
	"\x12ErrorCode_NotAnEgg\x10\xb1\x1f\x12 \n" +
	"\x1bErrorCode_ItemCannotUseHere\x10\xb2\x1f\x12\x1d\n" +
	"\x18ErrorCode_ConfigNotFound\x10\xb3\x1f\x12\x1e\n" +
	"\x19ErrorCode_PokemonNotInBox\x10\xb4\x1f\x12\x1a\n" +
	"\x15ErrorCode_CannotBreed\x10\xb5\x1f\x12\x1c\n" +
	"\x17ErrorCode_DatabaseError\x10\x89'\x12 \n" +
	"\x1bErrorCode_TransactionFailed\x10\x8a'\x12!\n" +
	"\x1cErrorCode_SerializationError\x10\x8b'\x12\x1b\n" +
	"\x16ErrorCode_NetworkError\x10\x8c'\x12\x1c\n" +
	"\x17ErrorCode_InternalError\x10\x8d'\x12!\n" +
	"\x1cErrorCode_ServiceUnavailable\x10\x8e'\x12\x16\n" +
	"\x11ErrorCode_Timeout\x10\x8f'\x12\x1a\n" +
	"\x15ErrorCode_ConfigError\x10\x90'\x12#\n" +
	"\x1eErrorCode_BattleServerNotFound\x10\xf1.\x12\x1e\n" +
	"\x19ErrorCode_BattleInfoError\x10\xf2.\x12 \n" +
	"\x1bErrorCode_CreateBattleError\x10\xf3.\x12!\n" +
	"\x1cErrorCode_BattleChoiceLocked\x10\xf4.\x12 \n" +
	"\x1bErrorCode_BattleChoiceEmpty\x10\xf5.\x12'\n" +
	"\"ErrorCode_BattleChoiceTitemInvalid\x10\xf6.B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ErrorCode_proto_rawDescOnce sync.Once
	file_MainServer_ErrorCode_proto_rawDescData []byte
)

func file_MainServer_ErrorCode_proto_rawDescGZIP() []byte {
	file_MainServer_ErrorCode_proto_rawDescOnce.Do(func() {
		file_MainServer_ErrorCode_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ErrorCode_proto_rawDesc), len(file_MainServer_ErrorCode_proto_rawDesc)))
	})
	return file_MainServer_ErrorCode_proto_rawDescData
}

var file_MainServer_ErrorCode_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_ErrorCode_proto_goTypes = []any{
	(ErrorCode)(0), // 0: MainServer.ErrorCode
}
var file_MainServer_ErrorCode_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_ErrorCode_proto_init() }
func file_MainServer_ErrorCode_proto_init() {
	if File_MainServer_ErrorCode_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ErrorCode_proto_rawDesc), len(file_MainServer_ErrorCode_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ErrorCode_proto_goTypes,
		DependencyIndexes: file_MainServer_ErrorCode_proto_depIdxs,
		EnumInfos:         file_MainServer_ErrorCode_proto_enumTypes,
	}.Build()
	File_MainServer_ErrorCode_proto = out.File
	file_MainServer_ErrorCode_proto_goTypes = nil
	file_MainServer_ErrorCode_proto_depIdxs = nil
}
