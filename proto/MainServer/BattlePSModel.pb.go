// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/BattlePSModel.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ==============================
// 主请求结构
// ==============================
type BattleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Active        []*ActiveInfo          `protobuf:"bytes,1,rep,name=active,proto3" json:"active,omitempty"`
	Side          *SideInfo              `protobuf:"bytes,2,opt,name=side,proto3" json:"side,omitempty"`
	TeamPreview   bool                   `protobuf:"varint,3,opt,name=teamPreview,proto3" json:"teamPreview,omitempty"`
	ForceSwitch   []bool                 `protobuf:"varint,4,rep,packed,name=forceSwitch,proto3" json:"forceSwitch,omitempty"`
	Wait          bool                   `protobuf:"varint,5,opt,name=wait,proto3" json:"wait,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleRequest) Reset() {
	*x = BattleRequest{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleRequest) ProtoMessage() {}

func (x *BattleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleRequest.ProtoReflect.Descriptor instead.
func (*BattleRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{0}
}

func (x *BattleRequest) GetActive() []*ActiveInfo {
	if x != nil {
		return x.Active
	}
	return nil
}

func (x *BattleRequest) GetSide() *SideInfo {
	if x != nil {
		return x.Side
	}
	return nil
}

func (x *BattleRequest) GetTeamPreview() bool {
	if x != nil {
		return x.TeamPreview
	}
	return false
}

func (x *BattleRequest) GetForceSwitch() []bool {
	if x != nil {
		return x.ForceSwitch
	}
	return nil
}

func (x *BattleRequest) GetWait() bool {
	if x != nil {
		return x.Wait
	}
	return false
}

// ==============================
// Active 区域（出战精灵信息）
// ==============================
type ActiveInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Moves           []*MoveInfo            `protobuf:"bytes,1,rep,name=moves,proto3" json:"moves,omitempty"`
	CanTerastallize string                 `protobuf:"bytes,2,opt,name=canTerastallize,proto3" json:"canTerastallize,omitempty"`
	CanDynamax      bool                   `protobuf:"varint,3,opt,name=canDynamax,proto3" json:"canDynamax,omitempty"`
	MaxMoves        *MaxMoveInfo           `protobuf:"bytes,4,opt,name=maxMoves,proto3" json:"maxMoves,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ActiveInfo) Reset() {
	*x = ActiveInfo{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveInfo) ProtoMessage() {}

func (x *ActiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveInfo.ProtoReflect.Descriptor instead.
func (*ActiveInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{1}
}

func (x *ActiveInfo) GetMoves() []*MoveInfo {
	if x != nil {
		return x.Moves
	}
	return nil
}

func (x *ActiveInfo) GetCanTerastallize() string {
	if x != nil {
		return x.CanTerastallize
	}
	return ""
}

func (x *ActiveInfo) GetCanDynamax() bool {
	if x != nil {
		return x.CanDynamax
	}
	return false
}

func (x *ActiveInfo) GetMaxMoves() *MaxMoveInfo {
	if x != nil {
		return x.MaxMoves
	}
	return nil
}

// ==============================
// 极巨化招式
// ==============================
type MaxMoveInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MaxMoves      []*MoveInfo            `protobuf:"bytes,1,rep,name=maxMoves,proto3" json:"maxMoves,omitempty"`
	Gigantamax    string                 `protobuf:"bytes,2,opt,name=gigantamax,proto3" json:"gigantamax,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaxMoveInfo) Reset() {
	*x = MaxMoveInfo{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaxMoveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaxMoveInfo) ProtoMessage() {}

func (x *MaxMoveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaxMoveInfo.ProtoReflect.Descriptor instead.
func (*MaxMoveInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{2}
}

func (x *MaxMoveInfo) GetMaxMoves() []*MoveInfo {
	if x != nil {
		return x.MaxMoves
	}
	return nil
}

func (x *MaxMoveInfo) GetGigantamax() string {
	if x != nil {
		return x.Gigantamax
	}
	return ""
}

// ==============================
// 招式信息
// ==============================
type MoveInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Move          string                 `protobuf:"bytes,1,opt,name=move,proto3" json:"move,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Pp            int32                  `protobuf:"varint,3,opt,name=pp,proto3" json:"pp,omitempty"`
	Maxpp         int32                  `protobuf:"varint,4,opt,name=maxpp,proto3" json:"maxpp,omitempty"`
	Target        string                 `protobuf:"bytes,5,opt,name=target,proto3" json:"target,omitempty"`
	Disabled      bool                   `protobuf:"varint,6,opt,name=disabled,proto3" json:"disabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoveInfo) Reset() {
	*x = MoveInfo{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveInfo) ProtoMessage() {}

func (x *MoveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveInfo.ProtoReflect.Descriptor instead.
func (*MoveInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{3}
}

func (x *MoveInfo) GetMove() string {
	if x != nil {
		return x.Move
	}
	return ""
}

func (x *MoveInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MoveInfo) GetPp() int32 {
	if x != nil {
		return x.Pp
	}
	return 0
}

func (x *MoveInfo) GetMaxpp() int32 {
	if x != nil {
		return x.Maxpp
	}
	return 0
}

func (x *MoveInfo) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *MoveInfo) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

// ==============================
// 队伍信息（自己方/对方）
// ==============================
type SideInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Pokemon       []*PokemonInfo         `protobuf:"bytes,3,rep,name=pokemon,proto3" json:"pokemon,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SideInfo) Reset() {
	*x = SideInfo{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SideInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SideInfo) ProtoMessage() {}

func (x *SideInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SideInfo.ProtoReflect.Descriptor instead.
func (*SideInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{4}
}

func (x *SideInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SideInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SideInfo) GetPokemon() []*PokemonInfo {
	if x != nil {
		return x.Pokemon
	}
	return nil
}

// ==============================
// 宝可梦信息
// ==============================
type PokemonInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ident         string                 `protobuf:"bytes,1,opt,name=ident,proto3" json:"ident,omitempty"`
	Details       string                 `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
	Condition     string                 `protobuf:"bytes,3,opt,name=condition,proto3" json:"condition,omitempty"`
	Active        bool                   `protobuf:"varint,4,opt,name=active,proto3" json:"active,omitempty"`
	Stats         *PsModelStats          `protobuf:"bytes,5,opt,name=stats,proto3" json:"stats,omitempty"`
	Moves         []string               `protobuf:"bytes,6,rep,name=moves,proto3" json:"moves,omitempty"`
	BaseAbility   string                 `protobuf:"bytes,7,opt,name=baseAbility,proto3" json:"baseAbility,omitempty"`
	Item          string                 `protobuf:"bytes,8,opt,name=item,proto3" json:"item,omitempty"`
	Pokeball      string                 `protobuf:"bytes,9,opt,name=pokeball,proto3" json:"pokeball,omitempty"`
	Ability       string                 `protobuf:"bytes,10,opt,name=ability,proto3" json:"ability,omitempty"`
	Commanding    bool                   `protobuf:"varint,11,opt,name=commanding,proto3" json:"commanding,omitempty"`
	Reviving      bool                   `protobuf:"varint,12,opt,name=reviving,proto3" json:"reviving,omitempty"`
	TeraType      string                 `protobuf:"bytes,13,opt,name=teraType,proto3" json:"teraType,omitempty"`
	Terastallized string                 `protobuf:"bytes,14,opt,name=terastallized,proto3" json:"terastallized,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokemonInfo) Reset() {
	*x = PokemonInfo{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokemonInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokemonInfo) ProtoMessage() {}

func (x *PokemonInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokemonInfo.ProtoReflect.Descriptor instead.
func (*PokemonInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{5}
}

func (x *PokemonInfo) GetIdent() string {
	if x != nil {
		return x.Ident
	}
	return ""
}

func (x *PokemonInfo) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *PokemonInfo) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *PokemonInfo) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *PokemonInfo) GetStats() *PsModelStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *PokemonInfo) GetMoves() []string {
	if x != nil {
		return x.Moves
	}
	return nil
}

func (x *PokemonInfo) GetBaseAbility() string {
	if x != nil {
		return x.BaseAbility
	}
	return ""
}

func (x *PokemonInfo) GetItem() string {
	if x != nil {
		return x.Item
	}
	return ""
}

func (x *PokemonInfo) GetPokeball() string {
	if x != nil {
		return x.Pokeball
	}
	return ""
}

func (x *PokemonInfo) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *PokemonInfo) GetCommanding() bool {
	if x != nil {
		return x.Commanding
	}
	return false
}

func (x *PokemonInfo) GetReviving() bool {
	if x != nil {
		return x.Reviving
	}
	return false
}

func (x *PokemonInfo) GetTeraType() string {
	if x != nil {
		return x.TeraType
	}
	return ""
}

func (x *PokemonInfo) GetTerastallized() string {
	if x != nil {
		return x.Terastallized
	}
	return ""
}

// ==============================
// 宝可梦属性数据
// ==============================
type PsModelStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Atk           int32                  `protobuf:"varint,1,opt,name=atk,proto3" json:"atk,omitempty"`
	Def           int32                  `protobuf:"varint,2,opt,name=def,proto3" json:"def,omitempty"`
	Spa           int32                  `protobuf:"varint,3,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd           int32                  `protobuf:"varint,4,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe           int32                  `protobuf:"varint,5,opt,name=spe,proto3" json:"spe,omitempty"`
	Accuracy      int32                  `protobuf:"varint,6,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	Evasion       int32                  `protobuf:"varint,7,opt,name=evasion,proto3" json:"evasion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PsModelStats) Reset() {
	*x = PsModelStats{}
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PsModelStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PsModelStats) ProtoMessage() {}

func (x *PsModelStats) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattlePSModel_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PsModelStats.ProtoReflect.Descriptor instead.
func (*PsModelStats) Descriptor() ([]byte, []int) {
	return file_MainServer_BattlePSModel_proto_rawDescGZIP(), []int{6}
}

func (x *PsModelStats) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PsModelStats) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PsModelStats) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PsModelStats) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PsModelStats) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

func (x *PsModelStats) GetAccuracy() int32 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *PsModelStats) GetEvasion() int32 {
	if x != nil {
		return x.Evasion
	}
	return 0
}

var File_MainServer_BattlePSModel_proto protoreflect.FileDescriptor

const file_MainServer_BattlePSModel_proto_rawDesc = "" +
	"\n" +
	"\x1eMainServer/BattlePSModel.proto\x12\n" +
	"MainServer\"\xc1\x01\n" +
	"\rBattleRequest\x12.\n" +
	"\x06active\x18\x01 \x03(\v2\x16.MainServer.ActiveInfoR\x06active\x12(\n" +
	"\x04side\x18\x02 \x01(\v2\x14.MainServer.SideInfoR\x04side\x12 \n" +
	"\vteamPreview\x18\x03 \x01(\bR\vteamPreview\x12 \n" +
	"\vforceSwitch\x18\x04 \x03(\bR\vforceSwitch\x12\x12\n" +
	"\x04wait\x18\x05 \x01(\bR\x04wait\"\xb7\x01\n" +
	"\n" +
	"ActiveInfo\x12*\n" +
	"\x05moves\x18\x01 \x03(\v2\x14.MainServer.MoveInfoR\x05moves\x12(\n" +
	"\x0fcanTerastallize\x18\x02 \x01(\tR\x0fcanTerastallize\x12\x1e\n" +
	"\n" +
	"canDynamax\x18\x03 \x01(\bR\n" +
	"canDynamax\x123\n" +
	"\bmaxMoves\x18\x04 \x01(\v2\x17.MainServer.MaxMoveInfoR\bmaxMoves\"_\n" +
	"\vMaxMoveInfo\x120\n" +
	"\bmaxMoves\x18\x01 \x03(\v2\x14.MainServer.MoveInfoR\bmaxMoves\x12\x1e\n" +
	"\n" +
	"gigantamax\x18\x02 \x01(\tR\n" +
	"gigantamax\"\x88\x01\n" +
	"\bMoveInfo\x12\x12\n" +
	"\x04move\x18\x01 \x01(\tR\x04move\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x0e\n" +
	"\x02pp\x18\x03 \x01(\x05R\x02pp\x12\x14\n" +
	"\x05maxpp\x18\x04 \x01(\x05R\x05maxpp\x12\x16\n" +
	"\x06target\x18\x05 \x01(\tR\x06target\x12\x1a\n" +
	"\bdisabled\x18\x06 \x01(\bR\bdisabled\"a\n" +
	"\bSideInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x121\n" +
	"\apokemon\x18\x03 \x03(\v2\x17.MainServer.PokemonInfoR\apokemon\"\xa3\x03\n" +
	"\vPokemonInfo\x12\x14\n" +
	"\x05ident\x18\x01 \x01(\tR\x05ident\x12\x18\n" +
	"\adetails\x18\x02 \x01(\tR\adetails\x12\x1c\n" +
	"\tcondition\x18\x03 \x01(\tR\tcondition\x12\x16\n" +
	"\x06active\x18\x04 \x01(\bR\x06active\x12.\n" +
	"\x05stats\x18\x05 \x01(\v2\x18.MainServer.PsModelStatsR\x05stats\x12\x14\n" +
	"\x05moves\x18\x06 \x03(\tR\x05moves\x12 \n" +
	"\vbaseAbility\x18\a \x01(\tR\vbaseAbility\x12\x12\n" +
	"\x04item\x18\b \x01(\tR\x04item\x12\x1a\n" +
	"\bpokeball\x18\t \x01(\tR\bpokeball\x12\x18\n" +
	"\aability\x18\n" +
	" \x01(\tR\aability\x12\x1e\n" +
	"\n" +
	"commanding\x18\v \x01(\bR\n" +
	"commanding\x12\x1a\n" +
	"\breviving\x18\f \x01(\bR\breviving\x12\x1a\n" +
	"\bteraType\x18\r \x01(\tR\bteraType\x12$\n" +
	"\rterastallized\x18\x0e \x01(\tR\rterastallized\"\x9e\x01\n" +
	"\fPsModelStats\x12\x10\n" +
	"\x03atk\x18\x01 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x02 \x01(\x05R\x03def\x12\x10\n" +
	"\x03spa\x18\x03 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x04 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\x05 \x01(\x05R\x03spe\x12\x1a\n" +
	"\baccuracy\x18\x06 \x01(\x05R\baccuracy\x12\x18\n" +
	"\aevasion\x18\a \x01(\x05R\aevasionB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_BattlePSModel_proto_rawDescOnce sync.Once
	file_MainServer_BattlePSModel_proto_rawDescData []byte
)

func file_MainServer_BattlePSModel_proto_rawDescGZIP() []byte {
	file_MainServer_BattlePSModel_proto_rawDescOnce.Do(func() {
		file_MainServer_BattlePSModel_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_BattlePSModel_proto_rawDesc), len(file_MainServer_BattlePSModel_proto_rawDesc)))
	})
	return file_MainServer_BattlePSModel_proto_rawDescData
}

var file_MainServer_BattlePSModel_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_MainServer_BattlePSModel_proto_goTypes = []any{
	(*BattleRequest)(nil), // 0: MainServer.BattleRequest
	(*ActiveInfo)(nil),    // 1: MainServer.ActiveInfo
	(*MaxMoveInfo)(nil),   // 2: MainServer.MaxMoveInfo
	(*MoveInfo)(nil),      // 3: MainServer.MoveInfo
	(*SideInfo)(nil),      // 4: MainServer.SideInfo
	(*PokemonInfo)(nil),   // 5: MainServer.PokemonInfo
	(*PsModelStats)(nil),  // 6: MainServer.PsModelStats
}
var file_MainServer_BattlePSModel_proto_depIdxs = []int32{
	1, // 0: MainServer.BattleRequest.active:type_name -> MainServer.ActiveInfo
	4, // 1: MainServer.BattleRequest.side:type_name -> MainServer.SideInfo
	3, // 2: MainServer.ActiveInfo.moves:type_name -> MainServer.MoveInfo
	2, // 3: MainServer.ActiveInfo.maxMoves:type_name -> MainServer.MaxMoveInfo
	3, // 4: MainServer.MaxMoveInfo.maxMoves:type_name -> MainServer.MoveInfo
	5, // 5: MainServer.SideInfo.pokemon:type_name -> MainServer.PokemonInfo
	6, // 6: MainServer.PokemonInfo.stats:type_name -> MainServer.PsModelStats
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_MainServer_BattlePSModel_proto_init() }
func file_MainServer_BattlePSModel_proto_init() {
	if File_MainServer_BattlePSModel_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_BattlePSModel_proto_rawDesc), len(file_MainServer_BattlePSModel_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BattlePSModel_proto_goTypes,
		DependencyIndexes: file_MainServer_BattlePSModel_proto_depIdxs,
		MessageInfos:      file_MainServer_BattlePSModel_proto_msgTypes,
	}.Build()
	File_MainServer_BattlePSModel_proto = out.File
	file_MainServer_BattlePSModel_proto_goTypes = nil
	file_MainServer_BattlePSModel_proto_depIdxs = nil
}
