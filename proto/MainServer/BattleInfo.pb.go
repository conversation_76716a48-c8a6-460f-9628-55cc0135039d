// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/BattleInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BattleOutputMessageType int32

const (
	BattleOutputMessageType_Normal   BattleOutputMessageType = 0
	BattleOutputMessageType_Prepare  BattleOutputMessageType = 1
	BattleOutputMessageType_TeamAck  BattleOutputMessageType = 2
	BattleOutputMessageType_MatchEnd BattleOutputMessageType = 100
)

// Enum value maps for BattleOutputMessageType.
var (
	BattleOutputMessageType_name = map[int32]string{
		0:   "Normal",
		1:   "Prepare",
		2:   "TeamAck",
		100: "MatchEnd",
	}
	BattleOutputMessageType_value = map[string]int32{
		"Normal":   0,
		"Prepare":  1,
		"TeamAck":  2,
		"MatchEnd": 100,
	}
)

func (x BattleOutputMessageType) Enum() *BattleOutputMessageType {
	p := new(BattleOutputMessageType)
	*p = x
	return p
}

func (x BattleOutputMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleOutputMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[0].Descriptor()
}

func (BattleOutputMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[0]
}

func (x BattleOutputMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleOutputMessageType.Descriptor instead.
func (BattleOutputMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{0}
}

type BattleSpecialMove int32

const (
	BattleSpecialMove_SpecialMove_None         BattleSpecialMove = 0
	BattleSpecialMove_SpecialMove_ZMove        BattleSpecialMove = 1
	BattleSpecialMove_SpecialMove_Mega         BattleSpecialMove = 2
	BattleSpecialMove_SpecialMove_Max          BattleSpecialMove = 3
	BattleSpecialMove_SpecialMove_Terastallize BattleSpecialMove = 4
)

// Enum value maps for BattleSpecialMove.
var (
	BattleSpecialMove_name = map[int32]string{
		0: "SpecialMove_None",
		1: "SpecialMove_ZMove",
		2: "SpecialMove_Mega",
		3: "SpecialMove_Max",
		4: "SpecialMove_Terastallize",
	}
	BattleSpecialMove_value = map[string]int32{
		"SpecialMove_None":         0,
		"SpecialMove_ZMove":        1,
		"SpecialMove_Mega":         2,
		"SpecialMove_Max":          3,
		"SpecialMove_Terastallize": 4,
	}
)

func (x BattleSpecialMove) Enum() *BattleSpecialMove {
	p := new(BattleSpecialMove)
	*p = x
	return p
}

func (x BattleSpecialMove) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleSpecialMove) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[1].Descriptor()
}

func (BattleSpecialMove) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[1]
}

func (x BattleSpecialMove) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleSpecialMove.Descriptor instead.
func (BattleSpecialMove) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{1}
}

type BattleChoiceType int32

const (
	BattleChoiceType_Choice_GiveUp      BattleChoiceType = 0
	BattleChoiceType_Choice_Move        BattleChoiceType = 1
	BattleChoiceType_Choice_Titem       BattleChoiceType = 2
	BattleChoiceType_Choice_Switch      BattleChoiceType = 3
	BattleChoiceType_Choice_ForceSwitch BattleChoiceType = 4
	BattleChoiceType_Choice_TeamAck     BattleChoiceType = 5
	BattleChoiceType_Choice_Run         BattleChoiceType = 6
)

// Enum value maps for BattleChoiceType.
var (
	BattleChoiceType_name = map[int32]string{
		0: "Choice_GiveUp",
		1: "Choice_Move",
		2: "Choice_Titem",
		3: "Choice_Switch",
		4: "Choice_ForceSwitch",
		5: "Choice_TeamAck",
		6: "Choice_Run",
	}
	BattleChoiceType_value = map[string]int32{
		"Choice_GiveUp":      0,
		"Choice_Move":        1,
		"Choice_Titem":       2,
		"Choice_Switch":      3,
		"Choice_ForceSwitch": 4,
		"Choice_TeamAck":     5,
		"Choice_Run":         6,
	}
)

func (x BattleChoiceType) Enum() *BattleChoiceType {
	p := new(BattleChoiceType)
	*p = x
	return p
}

func (x BattleChoiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleChoiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[2].Descriptor()
}

func (BattleChoiceType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[2]
}

func (x BattleChoiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleChoiceType.Descriptor instead.
func (BattleChoiceType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{2}
}

type BattleDecisionType int32

const (
	BattleDecisionType_Choice BattleDecisionType = 0
	BattleDecisionType_Init   BattleDecisionType = 1
	BattleDecisionType_Exit   BattleDecisionType = 2 //投降 //表示这场战斗停止
)

// Enum value maps for BattleDecisionType.
var (
	BattleDecisionType_name = map[int32]string{
		0: "Choice",
		1: "Init",
		2: "Exit",
	}
	BattleDecisionType_value = map[string]int32{
		"Choice": 0,
		"Init":   1,
		"Exit":   2,
	}
)

func (x BattleDecisionType) Enum() *BattleDecisionType {
	p := new(BattleDecisionType)
	*p = x
	return p
}

func (x BattleDecisionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleDecisionType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[3].Descriptor()
}

func (BattleDecisionType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[3]
}

func (x BattleDecisionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleDecisionType.Descriptor instead.
func (BattleDecisionType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{3}
}

type BattleServerMessagePermission int32

const (
	BattleServerMessagePermission_Player      BattleServerMessagePermission = 0
	BattleServerMessagePermission_Audience    BattleServerMessagePermission = 1 //观众
	BattleServerMessagePermission_PlayerError BattleServerMessagePermission = 2
)

// Enum value maps for BattleServerMessagePermission.
var (
	BattleServerMessagePermission_name = map[int32]string{
		0: "Player",
		1: "Audience",
		2: "PlayerError",
	}
	BattleServerMessagePermission_value = map[string]int32{
		"Player":      0,
		"Audience":    1,
		"PlayerError": 2,
	}
)

func (x BattleServerMessagePermission) Enum() *BattleServerMessagePermission {
	p := new(BattleServerMessagePermission)
	*p = x
	return p
}

func (x BattleServerMessagePermission) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleServerMessagePermission) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[4].Descriptor()
}

func (BattleServerMessagePermission) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[4]
}

func (x BattleServerMessagePermission) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleServerMessagePermission.Descriptor instead.
func (BattleServerMessagePermission) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{4}
}

type BattleType int32

const (
	BattleType_BattleType_Unknow            BattleType = 0
	BattleType_AI_4                         BattleType = 99
	BattleType_AI_2                         BattleType = 98
	BattleType_Msg                          BattleType = 100
	BattleType_SingleWild                   BattleType = 101
	BattleType_SingleWildAndDoublePokemon   BattleType = 102
	BattleType_DoubleWild                   BattleType = 103
	BattleType_SingleNPC                    BattleType = 104
	BattleType_SingleNPCAndDoublePokemon    BattleType = 105
	BattleType_DoubleNPC                    BattleType = 106
	BattleType_SinglePlayer                 BattleType = 107
	BattleType_SinglePlayerAndDoublePokemon BattleType = 108
	BattleType_DoublePlayer                 BattleType = 109
	BattleType_MultiPlayerWild4v4           BattleType = 110
	BattleType_MultiPlayerNPC4v4            BattleType = 111
	BattleType_MultiPlayer4v4               BattleType = 112
)

// Enum value maps for BattleType.
var (
	BattleType_name = map[int32]string{
		0:   "BattleType_Unknow",
		99:  "AI_4",
		98:  "AI_2",
		100: "Msg",
		101: "SingleWild",
		102: "SingleWildAndDoublePokemon",
		103: "DoubleWild",
		104: "SingleNPC",
		105: "SingleNPCAndDoublePokemon",
		106: "DoubleNPC",
		107: "SinglePlayer",
		108: "SinglePlayerAndDoublePokemon",
		109: "DoublePlayer",
		110: "MultiPlayerWild4v4",
		111: "MultiPlayerNPC4v4",
		112: "MultiPlayer4v4",
	}
	BattleType_value = map[string]int32{
		"BattleType_Unknow":            0,
		"AI_4":                         99,
		"AI_2":                         98,
		"Msg":                          100,
		"SingleWild":                   101,
		"SingleWildAndDoublePokemon":   102,
		"DoubleWild":                   103,
		"SingleNPC":                    104,
		"SingleNPCAndDoublePokemon":    105,
		"DoubleNPC":                    106,
		"SinglePlayer":                 107,
		"SinglePlayerAndDoublePokemon": 108,
		"DoublePlayer":                 109,
		"MultiPlayerWild4v4":           110,
		"MultiPlayerNPC4v4":            111,
		"MultiPlayer4v4":               112,
	}
)

func (x BattleType) Enum() *BattleType {
	p := new(BattleType)
	*p = x
	return p
}

func (x BattleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[5].Descriptor()
}

func (BattleType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[5]
}

func (x BattleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleType.Descriptor instead.
func (BattleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{5}
}

// client -> nakama
type BattlePrepare struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	BattleType         BattleType             `protobuf:"varint,1,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	BattleMatchAiMaker *BattleMatchAiMaker    `protobuf:"bytes,2,opt,name=battleMatchAiMaker,proto3" json:"battleMatchAiMaker,omitempty"`
	BattleMatchMaker   *BattleMatchMaker      `protobuf:"bytes,3,opt,name=battleMatchMaker,proto3" json:"battleMatchMaker,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BattlePrepare) Reset() {
	*x = BattlePrepare{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattlePrepare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePrepare) ProtoMessage() {}

func (x *BattlePrepare) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePrepare.ProtoReflect.Descriptor instead.
func (*BattlePrepare) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{0}
}

func (x *BattlePrepare) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattlePrepare) GetBattleMatchAiMaker() *BattleMatchAiMaker {
	if x != nil {
		return x.BattleMatchAiMaker
	}
	return nil
}

func (x *BattlePrepare) GetBattleMatchMaker() *BattleMatchMaker {
	if x != nil {
		return x.BattleMatchMaker
	}
	return nil
}

type BattleOutputMessageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BattleId      string                 `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	BattleType    BattleType             `protobuf:"varint,2,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	Messages      []*BattleOutputMessage `protobuf:"bytes,3,rep,name=messages,proto3" json:"messages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleOutputMessageInfo) Reset() {
	*x = BattleOutputMessageInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleOutputMessageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleOutputMessageInfo) ProtoMessage() {}

func (x *BattleOutputMessageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleOutputMessageInfo.ProtoReflect.Descriptor instead.
func (*BattleOutputMessageInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{1}
}

func (x *BattleOutputMessageInfo) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleOutputMessageInfo) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleOutputMessageInfo) GetMessages() []*BattleOutputMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

// nakama -> client
type BattleOutputMessage struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	BattleId      string                            `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	BattleType    BattleType                        `protobuf:"varint,2,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	MessageType   BattleOutputMessageType           `protobuf:"varint,3,opt,name=messageType,proto3,enum=MainServer.BattleOutputMessageType" json:"messageType,omitempty"`
	TrainerInfos  []*BattlePrepareOutputTrainerInfo `protobuf:"bytes,4,rep,name=trainerInfos,proto3" json:"trainerInfos,omitempty"`
	BattleContent string                            `protobuf:"bytes,5,opt,name=battleContent,proto3" json:"battleContent,omitempty"`
	Ts            int64                             `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`
	PsPlayerKey   string                            `protobuf:"bytes,7,opt,name=psPlayerKey,proto3" json:"psPlayerKey,omitempty"`
	PokeExpInfos  *PokeExpInfos                     `protobuf:"bytes,8,opt,name=pokeExpInfos,proto3" json:"pokeExpInfos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleOutputMessage) Reset() {
	*x = BattleOutputMessage{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleOutputMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleOutputMessage) ProtoMessage() {}

func (x *BattleOutputMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleOutputMessage.ProtoReflect.Descriptor instead.
func (*BattleOutputMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{2}
}

func (x *BattleOutputMessage) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleOutputMessage) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleOutputMessage) GetMessageType() BattleOutputMessageType {
	if x != nil {
		return x.MessageType
	}
	return BattleOutputMessageType_Normal
}

func (x *BattleOutputMessage) GetTrainerInfos() []*BattlePrepareOutputTrainerInfo {
	if x != nil {
		return x.TrainerInfos
	}
	return nil
}

func (x *BattleOutputMessage) GetBattleContent() string {
	if x != nil {
		return x.BattleContent
	}
	return ""
}

func (x *BattleOutputMessage) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *BattleOutputMessage) GetPsPlayerKey() string {
	if x != nil {
		return x.PsPlayerKey
	}
	return ""
}

func (x *BattleOutputMessage) GetPokeExpInfos() *PokeExpInfos {
	if x != nil {
		return x.PokeExpInfos
	}
	return nil
}

type BattlePrepareOutputTrainerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trainer       *Trainer               `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
	PsPlayerKey   string                 `protobuf:"bytes,2,opt,name=psPlayerKey,proto3" json:"psPlayerKey,omitempty"` //p1,p2,p3,p4
	IsAi          bool                   `protobuf:"varint,3,opt,name=isAi,proto3" json:"isAi,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattlePrepareOutputTrainerInfo) Reset() {
	*x = BattlePrepareOutputTrainerInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattlePrepareOutputTrainerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePrepareOutputTrainerInfo) ProtoMessage() {}

func (x *BattlePrepareOutputTrainerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePrepareOutputTrainerInfo.ProtoReflect.Descriptor instead.
func (*BattlePrepareOutputTrainerInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{3}
}

func (x *BattlePrepareOutputTrainerInfo) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

func (x *BattlePrepareOutputTrainerInfo) GetPsPlayerKey() string {
	if x != nil {
		return x.PsPlayerKey
	}
	return ""
}

func (x *BattlePrepareOutputTrainerInfo) GetIsAi() bool {
	if x != nil {
		return x.IsAi
	}
	return false
}

// client -> nakama
type BattleClientDecisionMessage struct {
	state                   protoimpl.MessageState              `protogen:"open.v1"`
	DecisionType            BattleDecisionType                  `protobuf:"varint,1,opt,name=decisionType,proto3,enum=MainServer.BattleDecisionType" json:"decisionType,omitempty"`
	Choices                 []*BattleClientDecisionChoiceInfo   `protobuf:"bytes,2,rep,name=choices,proto3" json:"choices,omitempty"`
	ThroughPointInfoRequest *UpdateQuestThroughPointInfoRequest `protobuf:"bytes,3,opt,name=throughPointInfoRequest,proto3" json:"throughPointInfoRequest,omitempty"` //战斗结束后更新Quest
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *BattleClientDecisionMessage) Reset() {
	*x = BattleClientDecisionMessage{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleClientDecisionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleClientDecisionMessage) ProtoMessage() {}

func (x *BattleClientDecisionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleClientDecisionMessage.ProtoReflect.Descriptor instead.
func (*BattleClientDecisionMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{4}
}

func (x *BattleClientDecisionMessage) GetDecisionType() BattleDecisionType {
	if x != nil {
		return x.DecisionType
	}
	return BattleDecisionType_Choice
}

func (x *BattleClientDecisionMessage) GetChoices() []*BattleClientDecisionChoiceInfo {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *BattleClientDecisionMessage) GetThroughPointInfoRequest() *UpdateQuestThroughPointInfoRequest {
	if x != nil {
		return x.ThroughPointInfoRequest
	}
	return nil
}

type BattleClientDecisionChoiceInfo struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Slot       int32                  `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"` //精灵位置
	TargetSlot int32                  `protobuf:"varint,2,opt,name=targetSlot,proto3" json:"targetSlot,omitempty"`
	ChoiceType BattleChoiceType       `protobuf:"varint,3,opt,name=choiceType,proto3,enum=MainServer.BattleChoiceType" json:"choiceType,omitempty"`
	Choice     string                 `protobuf:"bytes,4,opt,name=choice,proto3" json:"choice,omitempty"`
	// string move = 2; //使用技能
	// string switch = 3; //切换精灵
	// bool titem = 4; //使用道具
	// bool exit = 5; //逃跑
	Special       BattleSpecialMove `protobuf:"varint,5,opt,name=special,proto3,enum=MainServer.BattleSpecialMove" json:"special,omitempty"` //比如mega
	InventoryId   int64             `protobuf:"varint,6,opt,name=inventoryId,proto3" json:"inventoryId,omitempty"`                           //使用的道具id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleClientDecisionChoiceInfo) Reset() {
	*x = BattleClientDecisionChoiceInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleClientDecisionChoiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleClientDecisionChoiceInfo) ProtoMessage() {}

func (x *BattleClientDecisionChoiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleClientDecisionChoiceInfo.ProtoReflect.Descriptor instead.
func (*BattleClientDecisionChoiceInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{5}
}

func (x *BattleClientDecisionChoiceInfo) GetSlot() int32 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *BattleClientDecisionChoiceInfo) GetTargetSlot() int32 {
	if x != nil {
		return x.TargetSlot
	}
	return 0
}

func (x *BattleClientDecisionChoiceInfo) GetChoiceType() BattleChoiceType {
	if x != nil {
		return x.ChoiceType
	}
	return BattleChoiceType_Choice_GiveUp
}

func (x *BattleClientDecisionChoiceInfo) GetChoice() string {
	if x != nil {
		return x.Choice
	}
	return ""
}

func (x *BattleClientDecisionChoiceInfo) GetSpecial() BattleSpecialMove {
	if x != nil {
		return x.Special
	}
	return BattleSpecialMove_SpecialMove_None
}

func (x *BattleClientDecisionChoiceInfo) GetInventoryId() int64 {
	if x != nil {
		return x.InventoryId
	}
	return 0
}

// nakama -> ps
type BattleDecisionMessage struct {
	state        protoimpl.MessageState              `protogen:"open.v1"`
	BattleId     string                              `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	BattleType   BattleType                          `protobuf:"varint,2,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	DecisionType BattleDecisionType                  `protobuf:"varint,3,opt,name=decisionType,proto3,enum=MainServer.BattleDecisionType" json:"decisionType,omitempty"`
	Choices      map[int64]*BattleDecisionChoiceInfo `protobuf:"bytes,4,rep,name=choices,proto3" json:"choices,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` //tid -> choice
	// repeated BattleDecisionChoiceInfo choices = 4;
	// string content = 3;
	TeamInfos     []*BattleInitTeamInfo `protobuf:"bytes,5,rep,name=teamInfos,proto3" json:"teamInfos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleDecisionMessage) Reset() {
	*x = BattleDecisionMessage{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleDecisionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleDecisionMessage) ProtoMessage() {}

func (x *BattleDecisionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleDecisionMessage.ProtoReflect.Descriptor instead.
func (*BattleDecisionMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{6}
}

func (x *BattleDecisionMessage) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleDecisionMessage) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleDecisionMessage) GetDecisionType() BattleDecisionType {
	if x != nil {
		return x.DecisionType
	}
	return BattleDecisionType_Choice
}

func (x *BattleDecisionMessage) GetChoices() map[int64]*BattleDecisionChoiceInfo {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *BattleDecisionMessage) GetTeamInfos() []*BattleInitTeamInfo {
	if x != nil {
		return x.TeamInfos
	}
	return nil
}

// nakama -> ps
type BattleDecisionChoiceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tid           int64                  `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`
	Choice        string                 `protobuf:"bytes,2,opt,name=choice,proto3" json:"choice,omitempty"`
	Done          bool                   `protobuf:"varint,3,opt,name=done,proto3" json:"done,omitempty"`         //指令出现一个错误，则都用ai进行命令
	IsGiveup      bool                   `protobuf:"varint,4,opt,name=isGiveup,proto3" json:"isGiveup,omitempty"` //
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleDecisionChoiceInfo) Reset() {
	*x = BattleDecisionChoiceInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleDecisionChoiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleDecisionChoiceInfo) ProtoMessage() {}

func (x *BattleDecisionChoiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleDecisionChoiceInfo.ProtoReflect.Descriptor instead.
func (*BattleDecisionChoiceInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{7}
}

func (x *BattleDecisionChoiceInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleDecisionChoiceInfo) GetChoice() string {
	if x != nil {
		return x.Choice
	}
	return ""
}

func (x *BattleDecisionChoiceInfo) GetDone() bool {
	if x != nil {
		return x.Done
	}
	return false
}

func (x *BattleDecisionChoiceInfo) GetIsGiveup() bool {
	if x != nil {
		return x.IsGiveup
	}
	return false
}

type BattleInitTeamInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// int64 tid = 1;
	Team          string                          `protobuf:"bytes,1,opt,name=team,proto3" json:"team,omitempty"`
	TrainerInfo   *BattlePrepareOutputTrainerInfo `protobuf:"bytes,2,opt,name=trainerInfo,proto3" json:"trainerInfo,omitempty"`
	PokeStatus    map[int64]*BattleInitPokeStatus `protobuf:"bytes,3,rep,name=pokeStatus,proto3" json:"pokeStatus,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleInitTeamInfo) Reset() {
	*x = BattleInitTeamInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleInitTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleInitTeamInfo) ProtoMessage() {}

func (x *BattleInitTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleInitTeamInfo.ProtoReflect.Descriptor instead.
func (*BattleInitTeamInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{8}
}

func (x *BattleInitTeamInfo) GetTeam() string {
	if x != nil {
		return x.Team
	}
	return ""
}

func (x *BattleInitTeamInfo) GetTrainerInfo() *BattlePrepareOutputTrainerInfo {
	if x != nil {
		return x.TrainerInfo
	}
	return nil
}

func (x *BattleInitTeamInfo) GetPokeStatus() map[int64]*BattleInitPokeStatus {
	if x != nil {
		return x.PokeStatus
	}
	return nil
}

type BattleInitPokeStatus struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid              int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	PokeBoostStat    *PokeBoostStat         `protobuf:"bytes,3,opt,name=pokeBoostStat,proto3" json:"pokeBoostStat,omitempty"`                                    //初始的boost
	PokeBoostMinStat *PokeBoostStat         `protobuf:"bytes,4,opt,name=pokeBoostMinStat,proto3" json:"pokeBoostMinStat,omitempty"`                              //最低boost
	ImmunityType     []PokeTypeEnum         `protobuf:"varint,5,rep,packed,name=immunityType,proto3,enum=MainServer.PokeTypeEnum" json:"immunityType,omitempty"` //免疫类型
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BattleInitPokeStatus) Reset() {
	*x = BattleInitPokeStatus{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleInitPokeStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleInitPokeStatus) ProtoMessage() {}

func (x *BattleInitPokeStatus) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleInitPokeStatus.ProtoReflect.Descriptor instead.
func (*BattleInitPokeStatus) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{9}
}

func (x *BattleInitPokeStatus) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BattleInitPokeStatus) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleInitPokeStatus) GetPokeBoostStat() *PokeBoostStat {
	if x != nil {
		return x.PokeBoostStat
	}
	return nil
}

func (x *BattleInitPokeStatus) GetPokeBoostMinStat() *PokeBoostStat {
	if x != nil {
		return x.PokeBoostMinStat
	}
	return nil
}

func (x *BattleInitPokeStatus) GetImmunityType() []PokeTypeEnum {
	if x != nil {
		return x.ImmunityType
	}
	return nil
}

type BattleInitTrainerStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tid           int64                  `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`
	GetPokeRate   float32                `protobuf:"fixed32,2,opt,name=getPokeRate,proto3" json:"getPokeRate,omitempty"` //获取poke的概率
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleInitTrainerStatus) Reset() {
	*x = BattleInitTrainerStatus{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleInitTrainerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleInitTrainerStatus) ProtoMessage() {}

func (x *BattleInitTrainerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleInitTrainerStatus.ProtoReflect.Descriptor instead.
func (*BattleInitTrainerStatus) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{10}
}

func (x *BattleInitTrainerStatus) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleInitTrainerStatus) GetGetPokeRate() float32 {
	if x != nil {
		return x.GetPokeRate
	}
	return 0
}

// ps -> nakama
type BattleServerMessage struct {
	state               protoimpl.MessageState               `protogen:"open.v1"`
	BattleId            string                               `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	Tid                 int64                                `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Permission          BattleServerMessagePermission        `protobuf:"varint,3,opt,name=permission,proto3,enum=MainServer.BattleServerMessagePermission" json:"permission,omitempty"`
	Content             string                               `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	NeedUpdatePokeInfos []*BattleServerMessageUpdatePokeInfo `protobuf:"bytes,5,rep,name=needUpdatePokeInfos,proto3" json:"needUpdatePokeInfos,omitempty"`
	WinInfos            []*BattleServerMessageWinInfo        `protobuf:"bytes,6,rep,name=winInfos,proto3" json:"winInfos,omitempty"`
	TitemInfos          []*BattleServerMessageTitemInfo      `protobuf:"bytes,7,rep,name=titemInfos,proto3" json:"titemInfos,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BattleServerMessage) Reset() {
	*x = BattleServerMessage{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleServerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessage) ProtoMessage() {}

func (x *BattleServerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessage.ProtoReflect.Descriptor instead.
func (*BattleServerMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{11}
}

func (x *BattleServerMessage) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleServerMessage) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleServerMessage) GetPermission() BattleServerMessagePermission {
	if x != nil {
		return x.Permission
	}
	return BattleServerMessagePermission_Player
}

func (x *BattleServerMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BattleServerMessage) GetNeedUpdatePokeInfos() []*BattleServerMessageUpdatePokeInfo {
	if x != nil {
		return x.NeedUpdatePokeInfos
	}
	return nil
}

func (x *BattleServerMessage) GetWinInfos() []*BattleServerMessageWinInfo {
	if x != nil {
		return x.WinInfos
	}
	return nil
}

func (x *BattleServerMessage) GetTitemInfos() []*BattleServerMessageTitemInfo {
	if x != nil {
		return x.TitemInfos
	}
	return nil
}

type BattleServerMessageUpdatePokeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	HpSub         int32                  `protobuf:"varint,3,opt,name=hpSub,proto3" json:"hpSub,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleServerMessageUpdatePokeInfo) Reset() {
	*x = BattleServerMessageUpdatePokeInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleServerMessageUpdatePokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessageUpdatePokeInfo) ProtoMessage() {}

func (x *BattleServerMessageUpdatePokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessageUpdatePokeInfo.ProtoReflect.Descriptor instead.
func (*BattleServerMessageUpdatePokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{12}
}

func (x *BattleServerMessageUpdatePokeInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BattleServerMessageUpdatePokeInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleServerMessageUpdatePokeInfo) GetHpSub() int32 {
	if x != nil {
		return x.HpSub
	}
	return 0
}

type BattleServerMessageWinInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WinPartId     string                 `protobuf:"bytes,1,opt,name=winPartId,proto3" json:"winPartId,omitempty"`
	WinTids       []int64                `protobuf:"varint,2,rep,packed,name=winTids,proto3" json:"winTids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleServerMessageWinInfo) Reset() {
	*x = BattleServerMessageWinInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleServerMessageWinInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessageWinInfo) ProtoMessage() {}

func (x *BattleServerMessageWinInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessageWinInfo.ProtoReflect.Descriptor instead.
func (*BattleServerMessageWinInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{13}
}

func (x *BattleServerMessageWinInfo) GetWinPartId() string {
	if x != nil {
		return x.WinPartId
	}
	return ""
}

func (x *BattleServerMessageWinInfo) GetWinTids() []int64 {
	if x != nil {
		return x.WinTids
	}
	return nil
}

type BattleServerMessageTitemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Titem         string                 `protobuf:"bytes,1,opt,name=titem,proto3" json:"titem,omitempty"`
	TargetId      int64                  `protobuf:"varint,2,opt,name=targetId,proto3" json:"targetId,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleServerMessageTitemInfo) Reset() {
	*x = BattleServerMessageTitemInfo{}
	mi := &file_MainServer_BattleInfo_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleServerMessageTitemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessageTitemInfo) ProtoMessage() {}

func (x *BattleServerMessageTitemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessageTitemInfo.ProtoReflect.Descriptor instead.
func (*BattleServerMessageTitemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{14}
}

func (x *BattleServerMessageTitemInfo) GetTitem() string {
	if x != nil {
		return x.Titem
	}
	return ""
}

func (x *BattleServerMessageTitemInfo) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *BattleServerMessageTitemInfo) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_MainServer_BattleInfo_proto protoreflect.FileDescriptor

const file_MainServer_BattleInfo_proto_rawDesc = "" +
	"\n" +
	"\x1bMainServer/BattleInfo.proto\x12\n" +
	"MainServer\x1a\x18MainServer/Trainer.proto\x1a\x1cMainServer/BattleMatch.proto\x1a\x15MainServer/Poke.proto\x1a\x1dMainServer/TrainerQuest.proto\x1a\x18MainServer/PokeExp.proto\"\xe1\x01\n" +
	"\rBattlePrepare\x126\n" +
	"\n" +
	"battleType\x18\x01 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\x12N\n" +
	"\x12battleMatchAiMaker\x18\x02 \x01(\v2\x1e.MainServer.BattleMatchAiMakerR\x12battleMatchAiMaker\x12H\n" +
	"\x10battleMatchMaker\x18\x03 \x01(\v2\x1c.MainServer.BattleMatchMakerR\x10battleMatchMaker\"\xaa\x01\n" +
	"\x17BattleOutputMessageInfo\x12\x1a\n" +
	"\bbattleId\x18\x01 \x01(\tR\bbattleId\x126\n" +
	"\n" +
	"battleType\x18\x02 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\x12;\n" +
	"\bmessages\x18\x03 \x03(\v2\x1f.MainServer.BattleOutputMessageR\bmessages\"\x96\x03\n" +
	"\x13BattleOutputMessage\x12\x1a\n" +
	"\bbattleId\x18\x01 \x01(\tR\bbattleId\x126\n" +
	"\n" +
	"battleType\x18\x02 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\x12E\n" +
	"\vmessageType\x18\x03 \x01(\x0e2#.MainServer.BattleOutputMessageTypeR\vmessageType\x12N\n" +
	"\ftrainerInfos\x18\x04 \x03(\v2*.MainServer.BattlePrepareOutputTrainerInfoR\ftrainerInfos\x12$\n" +
	"\rbattleContent\x18\x05 \x01(\tR\rbattleContent\x12\x0e\n" +
	"\x02ts\x18\x06 \x01(\x03R\x02ts\x12 \n" +
	"\vpsPlayerKey\x18\a \x01(\tR\vpsPlayerKey\x12<\n" +
	"\fpokeExpInfos\x18\b \x01(\v2\x18.MainServer.PokeExpInfosR\fpokeExpInfos\"\x85\x01\n" +
	"\x1eBattlePrepareOutputTrainerInfo\x12-\n" +
	"\atrainer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\atrainer\x12 \n" +
	"\vpsPlayerKey\x18\x02 \x01(\tR\vpsPlayerKey\x12\x12\n" +
	"\x04isAi\x18\x03 \x01(\bR\x04isAi\"\x91\x02\n" +
	"\x1bBattleClientDecisionMessage\x12B\n" +
	"\fdecisionType\x18\x01 \x01(\x0e2\x1e.MainServer.BattleDecisionTypeR\fdecisionType\x12D\n" +
	"\achoices\x18\x02 \x03(\v2*.MainServer.BattleClientDecisionChoiceInfoR\achoices\x12h\n" +
	"\x17throughPointInfoRequest\x18\x03 \x01(\v2..MainServer.UpdateQuestThroughPointInfoRequestR\x17throughPointInfoRequest\"\x85\x02\n" +
	"\x1eBattleClientDecisionChoiceInfo\x12\x12\n" +
	"\x04slot\x18\x01 \x01(\x05R\x04slot\x12\x1e\n" +
	"\n" +
	"targetSlot\x18\x02 \x01(\x05R\n" +
	"targetSlot\x12<\n" +
	"\n" +
	"choiceType\x18\x03 \x01(\x0e2\x1c.MainServer.BattleChoiceTypeR\n" +
	"choiceType\x12\x16\n" +
	"\x06choice\x18\x04 \x01(\tR\x06choice\x127\n" +
	"\aspecial\x18\x05 \x01(\x0e2\x1d.MainServer.BattleSpecialMoveR\aspecial\x12 \n" +
	"\vinventoryId\x18\x06 \x01(\x03R\vinventoryId\"\x99\x03\n" +
	"\x15BattleDecisionMessage\x12\x1a\n" +
	"\bbattleId\x18\x01 \x01(\tR\bbattleId\x126\n" +
	"\n" +
	"battleType\x18\x02 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\x12B\n" +
	"\fdecisionType\x18\x03 \x01(\x0e2\x1e.MainServer.BattleDecisionTypeR\fdecisionType\x12H\n" +
	"\achoices\x18\x04 \x03(\v2..MainServer.BattleDecisionMessage.ChoicesEntryR\achoices\x12<\n" +
	"\tteamInfos\x18\x05 \x03(\v2\x1e.MainServer.BattleInitTeamInfoR\tteamInfos\x1a`\n" +
	"\fChoicesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12:\n" +
	"\x05value\x18\x02 \x01(\v2$.MainServer.BattleDecisionChoiceInfoR\x05value:\x028\x01\"t\n" +
	"\x18BattleDecisionChoiceInfo\x12\x10\n" +
	"\x03tid\x18\x01 \x01(\x03R\x03tid\x12\x16\n" +
	"\x06choice\x18\x02 \x01(\tR\x06choice\x12\x12\n" +
	"\x04done\x18\x03 \x01(\bR\x04done\x12\x1a\n" +
	"\bisGiveup\x18\x04 \x01(\bR\bisGiveup\"\xa7\x02\n" +
	"\x12BattleInitTeamInfo\x12\x12\n" +
	"\x04team\x18\x01 \x01(\tR\x04team\x12L\n" +
	"\vtrainerInfo\x18\x02 \x01(\v2*.MainServer.BattlePrepareOutputTrainerInfoR\vtrainerInfo\x12N\n" +
	"\n" +
	"pokeStatus\x18\x03 \x03(\v2..MainServer.BattleInitTeamInfo.PokeStatusEntryR\n" +
	"pokeStatus\x1a_\n" +
	"\x0fPokeStatusEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x126\n" +
	"\x05value\x18\x02 \x01(\v2 .MainServer.BattleInitPokeStatusR\x05value:\x028\x01\"\xfe\x01\n" +
	"\x14BattleInitPokeStatus\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12?\n" +
	"\rpokeBoostStat\x18\x03 \x01(\v2\x19.MainServer.PokeBoostStatR\rpokeBoostStat\x12E\n" +
	"\x10pokeBoostMinStat\x18\x04 \x01(\v2\x19.MainServer.PokeBoostStatR\x10pokeBoostMinStat\x12<\n" +
	"\fimmunityType\x18\x05 \x03(\x0e2\x18.MainServer.PokeTypeEnumR\fimmunityType\"M\n" +
	"\x17BattleInitTrainerStatus\x12\x10\n" +
	"\x03tid\x18\x01 \x01(\x03R\x03tid\x12 \n" +
	"\vgetPokeRate\x18\x02 \x01(\x02R\vgetPokeRate\"\x97\x03\n" +
	"\x13BattleServerMessage\x12\x1a\n" +
	"\bbattleId\x18\x01 \x01(\tR\bbattleId\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12I\n" +
	"\n" +
	"permission\x18\x03 \x01(\x0e2).MainServer.BattleServerMessagePermissionR\n" +
	"permission\x12\x18\n" +
	"\acontent\x18\x04 \x01(\tR\acontent\x12_\n" +
	"\x13needUpdatePokeInfos\x18\x05 \x03(\v2-.MainServer.BattleServerMessageUpdatePokeInfoR\x13needUpdatePokeInfos\x12B\n" +
	"\bwinInfos\x18\x06 \x03(\v2&.MainServer.BattleServerMessageWinInfoR\bwinInfos\x12H\n" +
	"\n" +
	"titemInfos\x18\a \x03(\v2(.MainServer.BattleServerMessageTitemInfoR\n" +
	"titemInfos\"[\n" +
	"!BattleServerMessageUpdatePokeInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x14\n" +
	"\x05hpSub\x18\x03 \x01(\x05R\x05hpSub\"T\n" +
	"\x1aBattleServerMessageWinInfo\x12\x1c\n" +
	"\twinPartId\x18\x01 \x01(\tR\twinPartId\x12\x18\n" +
	"\awinTids\x18\x02 \x03(\x03R\awinTids\"j\n" +
	"\x1cBattleServerMessageTitemInfo\x12\x14\n" +
	"\x05titem\x18\x01 \x01(\tR\x05titem\x12\x1a\n" +
	"\btargetId\x18\x02 \x01(\x03R\btargetId\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess*M\n" +
	"\x17BattleOutputMessageType\x12\n" +
	"\n" +
	"\x06Normal\x10\x00\x12\v\n" +
	"\aPrepare\x10\x01\x12\v\n" +
	"\aTeamAck\x10\x02\x12\f\n" +
	"\bMatchEnd\x10d*\x89\x01\n" +
	"\x11BattleSpecialMove\x12\x14\n" +
	"\x10SpecialMove_None\x10\x00\x12\x15\n" +
	"\x11SpecialMove_ZMove\x10\x01\x12\x14\n" +
	"\x10SpecialMove_Mega\x10\x02\x12\x13\n" +
	"\x0fSpecialMove_Max\x10\x03\x12\x1c\n" +
	"\x18SpecialMove_Terastallize\x10\x04*\x97\x01\n" +
	"\x10BattleChoiceType\x12\x11\n" +
	"\rChoice_GiveUp\x10\x00\x12\x0f\n" +
	"\vChoice_Move\x10\x01\x12\x10\n" +
	"\fChoice_Titem\x10\x02\x12\x11\n" +
	"\rChoice_Switch\x10\x03\x12\x16\n" +
	"\x12Choice_ForceSwitch\x10\x04\x12\x12\n" +
	"\x0eChoice_TeamAck\x10\x05\x12\x0e\n" +
	"\n" +
	"Choice_Run\x10\x06*4\n" +
	"\x12BattleDecisionType\x12\n" +
	"\n" +
	"\x06Choice\x10\x00\x12\b\n" +
	"\x04Init\x10\x01\x12\b\n" +
	"\x04Exit\x10\x02*J\n" +
	"\x1dBattleServerMessagePermission\x12\n" +
	"\n" +
	"\x06Player\x10\x00\x12\f\n" +
	"\bAudience\x10\x01\x12\x0f\n" +
	"\vPlayerError\x10\x02*\xc6\x02\n" +
	"\n" +
	"BattleType\x12\x15\n" +
	"\x11BattleType_Unknow\x10\x00\x12\b\n" +
	"\x04AI_4\x10c\x12\b\n" +
	"\x04AI_2\x10b\x12\a\n" +
	"\x03Msg\x10d\x12\x0e\n" +
	"\n" +
	"SingleWild\x10e\x12\x1e\n" +
	"\x1aSingleWildAndDoublePokemon\x10f\x12\x0e\n" +
	"\n" +
	"DoubleWild\x10g\x12\r\n" +
	"\tSingleNPC\x10h\x12\x1d\n" +
	"\x19SingleNPCAndDoublePokemon\x10i\x12\r\n" +
	"\tDoubleNPC\x10j\x12\x10\n" +
	"\fSinglePlayer\x10k\x12 \n" +
	"\x1cSinglePlayerAndDoublePokemon\x10l\x12\x10\n" +
	"\fDoublePlayer\x10m\x12\x16\n" +
	"\x12MultiPlayerWild4v4\x10n\x12\x15\n" +
	"\x11MultiPlayerNPC4v4\x10o\x12\x12\n" +
	"\x0eMultiPlayer4v4\x10pB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_BattleInfo_proto_rawDescOnce sync.Once
	file_MainServer_BattleInfo_proto_rawDescData []byte
)

func file_MainServer_BattleInfo_proto_rawDescGZIP() []byte {
	file_MainServer_BattleInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_BattleInfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_BattleInfo_proto_rawDesc), len(file_MainServer_BattleInfo_proto_rawDesc)))
	})
	return file_MainServer_BattleInfo_proto_rawDescData
}

var file_MainServer_BattleInfo_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_MainServer_BattleInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_MainServer_BattleInfo_proto_goTypes = []any{
	(BattleOutputMessageType)(0),               // 0: MainServer.BattleOutputMessageType
	(BattleSpecialMove)(0),                     // 1: MainServer.BattleSpecialMove
	(BattleChoiceType)(0),                      // 2: MainServer.BattleChoiceType
	(BattleDecisionType)(0),                    // 3: MainServer.BattleDecisionType
	(BattleServerMessagePermission)(0),         // 4: MainServer.BattleServerMessagePermission
	(BattleType)(0),                            // 5: MainServer.BattleType
	(*BattlePrepare)(nil),                      // 6: MainServer.BattlePrepare
	(*BattleOutputMessageInfo)(nil),            // 7: MainServer.BattleOutputMessageInfo
	(*BattleOutputMessage)(nil),                // 8: MainServer.BattleOutputMessage
	(*BattlePrepareOutputTrainerInfo)(nil),     // 9: MainServer.BattlePrepareOutputTrainerInfo
	(*BattleClientDecisionMessage)(nil),        // 10: MainServer.BattleClientDecisionMessage
	(*BattleClientDecisionChoiceInfo)(nil),     // 11: MainServer.BattleClientDecisionChoiceInfo
	(*BattleDecisionMessage)(nil),              // 12: MainServer.BattleDecisionMessage
	(*BattleDecisionChoiceInfo)(nil),           // 13: MainServer.BattleDecisionChoiceInfo
	(*BattleInitTeamInfo)(nil),                 // 14: MainServer.BattleInitTeamInfo
	(*BattleInitPokeStatus)(nil),               // 15: MainServer.BattleInitPokeStatus
	(*BattleInitTrainerStatus)(nil),            // 16: MainServer.BattleInitTrainerStatus
	(*BattleServerMessage)(nil),                // 17: MainServer.BattleServerMessage
	(*BattleServerMessageUpdatePokeInfo)(nil),  // 18: MainServer.BattleServerMessageUpdatePokeInfo
	(*BattleServerMessageWinInfo)(nil),         // 19: MainServer.BattleServerMessageWinInfo
	(*BattleServerMessageTitemInfo)(nil),       // 20: MainServer.BattleServerMessageTitemInfo
	nil,                                        // 21: MainServer.BattleDecisionMessage.ChoicesEntry
	nil,                                        // 22: MainServer.BattleInitTeamInfo.PokeStatusEntry
	(*BattleMatchAiMaker)(nil),                 // 23: MainServer.BattleMatchAiMaker
	(*BattleMatchMaker)(nil),                   // 24: MainServer.BattleMatchMaker
	(*PokeExpInfos)(nil),                       // 25: MainServer.PokeExpInfos
	(*Trainer)(nil),                            // 26: MainServer.Trainer
	(*UpdateQuestThroughPointInfoRequest)(nil), // 27: MainServer.UpdateQuestThroughPointInfoRequest
	(*PokeBoostStat)(nil),                      // 28: MainServer.PokeBoostStat
	(PokeTypeEnum)(0),                          // 29: MainServer.PokeTypeEnum
}
var file_MainServer_BattleInfo_proto_depIdxs = []int32{
	5,  // 0: MainServer.BattlePrepare.battleType:type_name -> MainServer.BattleType
	23, // 1: MainServer.BattlePrepare.battleMatchAiMaker:type_name -> MainServer.BattleMatchAiMaker
	24, // 2: MainServer.BattlePrepare.battleMatchMaker:type_name -> MainServer.BattleMatchMaker
	5,  // 3: MainServer.BattleOutputMessageInfo.battleType:type_name -> MainServer.BattleType
	8,  // 4: MainServer.BattleOutputMessageInfo.messages:type_name -> MainServer.BattleOutputMessage
	5,  // 5: MainServer.BattleOutputMessage.battleType:type_name -> MainServer.BattleType
	0,  // 6: MainServer.BattleOutputMessage.messageType:type_name -> MainServer.BattleOutputMessageType
	9,  // 7: MainServer.BattleOutputMessage.trainerInfos:type_name -> MainServer.BattlePrepareOutputTrainerInfo
	25, // 8: MainServer.BattleOutputMessage.pokeExpInfos:type_name -> MainServer.PokeExpInfos
	26, // 9: MainServer.BattlePrepareOutputTrainerInfo.trainer:type_name -> MainServer.Trainer
	3,  // 10: MainServer.BattleClientDecisionMessage.decisionType:type_name -> MainServer.BattleDecisionType
	11, // 11: MainServer.BattleClientDecisionMessage.choices:type_name -> MainServer.BattleClientDecisionChoiceInfo
	27, // 12: MainServer.BattleClientDecisionMessage.throughPointInfoRequest:type_name -> MainServer.UpdateQuestThroughPointInfoRequest
	2,  // 13: MainServer.BattleClientDecisionChoiceInfo.choiceType:type_name -> MainServer.BattleChoiceType
	1,  // 14: MainServer.BattleClientDecisionChoiceInfo.special:type_name -> MainServer.BattleSpecialMove
	5,  // 15: MainServer.BattleDecisionMessage.battleType:type_name -> MainServer.BattleType
	3,  // 16: MainServer.BattleDecisionMessage.decisionType:type_name -> MainServer.BattleDecisionType
	21, // 17: MainServer.BattleDecisionMessage.choices:type_name -> MainServer.BattleDecisionMessage.ChoicesEntry
	14, // 18: MainServer.BattleDecisionMessage.teamInfos:type_name -> MainServer.BattleInitTeamInfo
	9,  // 19: MainServer.BattleInitTeamInfo.trainerInfo:type_name -> MainServer.BattlePrepareOutputTrainerInfo
	22, // 20: MainServer.BattleInitTeamInfo.pokeStatus:type_name -> MainServer.BattleInitTeamInfo.PokeStatusEntry
	28, // 21: MainServer.BattleInitPokeStatus.pokeBoostStat:type_name -> MainServer.PokeBoostStat
	28, // 22: MainServer.BattleInitPokeStatus.pokeBoostMinStat:type_name -> MainServer.PokeBoostStat
	29, // 23: MainServer.BattleInitPokeStatus.immunityType:type_name -> MainServer.PokeTypeEnum
	4,  // 24: MainServer.BattleServerMessage.permission:type_name -> MainServer.BattleServerMessagePermission
	18, // 25: MainServer.BattleServerMessage.needUpdatePokeInfos:type_name -> MainServer.BattleServerMessageUpdatePokeInfo
	19, // 26: MainServer.BattleServerMessage.winInfos:type_name -> MainServer.BattleServerMessageWinInfo
	20, // 27: MainServer.BattleServerMessage.titemInfos:type_name -> MainServer.BattleServerMessageTitemInfo
	13, // 28: MainServer.BattleDecisionMessage.ChoicesEntry.value:type_name -> MainServer.BattleDecisionChoiceInfo
	15, // 29: MainServer.BattleInitTeamInfo.PokeStatusEntry.value:type_name -> MainServer.BattleInitPokeStatus
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_MainServer_BattleInfo_proto_init() }
func file_MainServer_BattleInfo_proto_init() {
	if File_MainServer_BattleInfo_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_BattleMatch_proto_init()
	file_MainServer_Poke_proto_init()
	file_MainServer_TrainerQuest_proto_init()
	file_MainServer_PokeExp_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_BattleInfo_proto_rawDesc), len(file_MainServer_BattleInfo_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BattleInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_BattleInfo_proto_depIdxs,
		EnumInfos:         file_MainServer_BattleInfo_proto_enumTypes,
		MessageInfos:      file_MainServer_BattleInfo_proto_msgTypes,
	}.Build()
	File_MainServer_BattleInfo_proto = out.File
	file_MainServer_BattleInfo_proto_goTypes = nil
	file_MainServer_BattleInfo_proto_depIdxs = nil
}
