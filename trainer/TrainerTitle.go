package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableTrainerTitle = "trainer_title"

// 每日称号操作配额（可配置）
const DefaultDailyTitleQuota = 1

// InitTrainerTitle 初始化训练师称号模块
func InitTrainerTitle(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTrainerTitleTableIfNotExists(ctx, logger, db)
}

// createTrainerTitleTableIfNotExists 创建训练师称号表
func createTrainerTitleTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTableSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,                    -- 自增长 ID
            tid BIGINT NOT NULL,                         -- 训练师ID
            type INT NOT NULL,                           -- 称号类型 (MainServer.TrainerTitleType)
            create_ts BIGINT NOT NULL,                   -- 创建时间戳
            update_ts BIGINT NOT NULL,                   -- 更新时间戳
            expire_ts BIGINT NOT NULL DEFAULT 0,         -- 过期时间戳 (0表示永不过期)
            count INT NOT NULL DEFAULT 1                 -- 可佩戴次数 (负数表示无限次)
        );`, TableTrainerTitle)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Failed to create table %s: %v", TableTrainerTitle, err)
	} else {
		logger.Info("Successfully created table %s", TableTrainerTitle)

		// 创建索引以优化查询性能
		createIndexSQL := fmt.Sprintf(`
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid ON %[1]s (tid);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_type ON %[1]s (type);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid_type ON %[1]s (tid, type);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_expire_ts ON %[1]s (expire_ts);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_count ON %[1]s (count);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_update_ts ON %[1]s (update_ts);
        `, TableTrainerTitle)

		_, err = db.ExecContext(ctx, createIndexSQL)
		if err != nil {
			logger.Error("Failed to create indexes on table %s: %v", TableTrainerTitle, err)
		} else {
			logger.Info("Successfully created indexes on table %s", TableTrainerTitle)
		}
	}
}

// AddTrainerTitle 添加训练师称号 expireTs为0表示永不过期 expireTs为负数表示已删除 count为负数表示无限次佩戴 count为0表示不可佩戴 count为正数表示可佩戴的次数 count为负数表示可永久佩戴
func AddTrainerTitle(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, titleType MainServer.TrainerTitleType, expireTs int64, count int32) error {
	nowTs := time.Now().Unix() // 精确到秒

	insertSQL := fmt.Sprintf(`
		INSERT INTO %s (tid, type, create_ts, update_ts, expire_ts, count)
		VALUES ($1, $2, $3, $4, $5, $6)
	`, TableTrainerTitle)

	_, err := tx.ExecContext(ctx, insertSQL, tid, int(titleType), nowTs, nowTs, expireTs, count)
	if err != nil {
		logger.Error("Failed to insert trainer title: %v", err)
		return runtime.NewError("Failed to add trainer title", 500)
	}

	logger.Info("Successfully added trainer title for tid %d, type %d, expireTs %d, count %d", tid, int(titleType), expireTs, count)
	return nil
}

// RemoveTrainerTitle 删除训练师称号 (设置expireTs为负数，count为0)
func RemoveTrainerTitle(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, titleType MainServer.TrainerTitleType) error {
	nowTs := time.Now().Unix()

	updateSQL := fmt.Sprintf(`
		UPDATE %s 
		SET expire_ts = -1, count = 0, update_ts = $1
		WHERE tid = $2 AND type = $3
	`, TableTrainerTitle)

	result, err := tx.ExecContext(ctx, updateSQL, nowTs, tid, int(titleType))
	if err != nil {
		logger.Error("Failed to remove trainer title: %v", err)
		return runtime.NewError("Failed to remove trainer title", 500)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("Failed to get rows affected: %v", err)
		return runtime.NewError("Failed to verify removal", 500)
	}

	if rowsAffected == 0 {
		return runtime.NewError("Title not found", 404)
	}

	logger.Info("Successfully removed trainer title for tid %d, type %d", tid, int(titleType))
	return nil
}

// GetAllTrainerTitles 获取训练师所有称号
func GetAllTrainerTitles(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerTitleInfo, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, type, create_ts, update_ts, expire_ts, count
		FROM %s 
		WHERE tid = $1
		ORDER BY create_ts DESC
	`, TableTrainerTitle)

	rows, err := tx.QueryContext(ctx, query, tid)
	if err != nil {
		logger.Error("Failed to query all trainer titles: %v", err)
		return nil, runtime.NewError("Failed to get trainer titles", 500)
	}
	defer rows.Close()

	var titles []*MainServer.TrainerTitleInfo
	for rows.Next() {
		title := &MainServer.TrainerTitleInfo{}
		var titleType int

		err := rows.Scan(
			&title.Id,
			&title.Tid,
			&titleType,
			&title.CreateTs,
			&title.UpdateTs,
			&title.ExpireTs,
			&title.Count,
		)
		if err != nil {
			logger.Error("Failed to scan title row: %v", err)
			return nil, runtime.NewError("Failed to parse title data", 500)
		}

		title.Type = MainServer.TrainerTitleType(titleType)
		titles = append(titles, title)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error during rows iteration: %v", err)
		return nil, runtime.NewError("Failed to process title data", 500)
	}

	logger.Info("Successfully retrieved %d titles for tid %d", len(titles), tid)
	return titles, nil
}

// GetUnavailableTrainerTitles 获取训练师不可用的称号 (expireTs > 0 或者 count <= 0)
func GetUnavailableTrainerTitles(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerTitleInfo, error) {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		SELECT id, tid, type, create_ts, update_ts, expire_ts, count
		FROM %s 
		WHERE tid = $1 AND (expire_ts > 0 AND expire_ts < $2) OR count <= 0
		ORDER BY create_ts DESC
	`, TableTrainerTitle)

	rows, err := tx.QueryContext(ctx, query, tid, nowTs)
	if err != nil {
		logger.Error("Failed to query unavailable trainer titles: %v", err)
		return nil, runtime.NewError("Failed to get unavailable trainer titles", 500)
	}
	defer rows.Close()

	var titles []*MainServer.TrainerTitleInfo
	for rows.Next() {
		title := &MainServer.TrainerTitleInfo{}
		var titleType int

		err := rows.Scan(
			&title.Id,
			&title.Tid,
			&titleType,
			&title.CreateTs,
			&title.UpdateTs,
			&title.ExpireTs,
			&title.Count,
		)
		if err != nil {
			logger.Error("Failed to scan title row: %v", err)
			return nil, runtime.NewError("Failed to parse title data", 500)
		}

		title.Type = MainServer.TrainerTitleType(titleType)
		titles = append(titles, title)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error during rows iteration: %v", err)
		return nil, runtime.NewError("Failed to process title data", 500)
	}

	logger.Info("Successfully retrieved %d unavailable titles for tid %d", len(titles), tid)
	return titles, nil
}

// WearTrainerTitle 佩戴训练师称号 (count -1，负数表示无限次佩戴)
func WearTrainerTitle(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, titleType MainServer.TrainerTitleType) error {
	nowTs := time.Now().Unix()

	// 首先检查称号是否可用
	checkQuery := fmt.Sprintf(`
		SELECT count, expire_ts 
		FROM %s 
		WHERE tid = $1 AND type = $2
	`, TableTrainerTitle)

	var count int32
	var expireTs int64
	err := tx.QueryRowContext(ctx, checkQuery, tid, int(titleType)).Scan(&count, &expireTs)
	if err != nil {
		if err == sql.ErrNoRows {
			return runtime.NewError("Title not found", 404)
		}
		logger.Error("Failed to check trainer title: %v", err)
		return runtime.NewError("Failed to check trainer title", 500)
	}

	// 检查是否过期
	if expireTs > 0 && expireTs < nowTs {
		return runtime.NewError("Title has expired", 400)
	}

	// 检查是否可以佩戴
	if count == 0 {
		return runtime.NewError("Title has no remaining uses", 400)
	}

	// 如果count为负数，表示无限次佩戴，不需要减少count
	if count > 0 {
		updateSQL := fmt.Sprintf(`
			UPDATE %s 
			SET count = count - 1, update_ts = $1
			WHERE tid = $2 AND type = $3
		`, TableTrainerTitle)

		_, err = tx.ExecContext(ctx, updateSQL, nowTs, tid, int(titleType))
		if err != nil {
			logger.Error("Failed to wear trainer title: %v", err)
			return runtime.NewError("Failed to wear trainer title", 500)
		}

		logger.Info("Successfully wore trainer title for tid %d, type %d, remaining count: %d", tid, int(titleType), count-1)
	} else {
		logger.Info("Successfully wore trainer title for tid %d, type %d (unlimited uses)", tid, int(titleType))
	}

	return nil
}

// GetTrainerTitleInfo 获取训练师指定类型的称号信息
func GetTrainerTitleInfo(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, titleType MainServer.TrainerTitleType) (*MainServer.TrainerTitleInfo, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, type, create_ts, update_ts, expire_ts, count
		FROM %s
		WHERE tid = $1 AND type = $2
	`, TableTrainerTitle)

	title := &MainServer.TrainerTitleInfo{}
	var titleTypeInt int

	err := tx.QueryRowContext(ctx, query, tid, int(titleType)).Scan(
		&title.Id,
		&title.Tid,
		&titleTypeInt,
		&title.CreateTs,
		&title.UpdateTs,
		&title.ExpireTs,
		&title.Count,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, runtime.NewError("Title not found", 404)
		}
		logger.Error("Failed to query trainer title info: %v", err)
		return nil, runtime.NewError("Failed to get trainer title info", 500)
	}

	title.Type = MainServer.TrainerTitleType(titleTypeInt)
	return title, nil
}

// GetDailyTitleQuota 获取每日称号操作配额（可配置）
func GetDailyTitleQuota() int32 {
	// 这里可以从配置文件、数据库或环境变量读取
	// 目前使用默认值
	return DefaultDailyTitleQuota
}

// RpcAddTrainerTitle RPC函数：添加训练师称号
// func RpcAddTrainerTitle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	var request MainServer.RpcAddTrainerTitleRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 添加称号
// 	err = AddTrainerTitle(ctx, logger, tx, trainer.Id, request.Type, request.ExpireTs, request.Count)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 返回成功结果
// 	result := &MainServer.RpcAddTrainerTitleResponse{
// 		Success: true,
// 		Message: "称号添加成功",
// 	}

// 	return tool.ProtoToBase64(result)
// }

// RpcRemoveTrainerTitle RPC函数：删除训练师称号
// func RpcRemoveTrainerTitle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	var request MainServer.RpcRemoveTrainerTitleRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 删除称号
// 	err = RemoveTrainerTitle(ctx, logger, tx, trainer.Id, request.Type)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 返回成功结果
// 	result := &MainServer.RpcRemoveTrainerTitleResponse{
// 		Success: true,
// 		Message: "称号删除成功",
// 	}

// 	return tool.ProtoToBase64(result)
// }

// RpcGetAllTrainerTitles RPC函数：获取训练师所有称号
func RpcGetAllTrainerTitles(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取所有称号
	titles, err := GetAllTrainerTitles(ctx, logger, tx, trainer.Id)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetAllTrainerTitlesResponse{
		Titles: titles,
	}

	return tool.ProtoToBase64(result)
}

// RpcGetUnavailableTrainerTitles RPC函数：获取训练师不可用的称号
func RpcGetUnavailableTrainerTitles(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取不可用称号
	titles, err := GetUnavailableTrainerTitles(ctx, logger, tx, trainer.Id)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetUnavailableTrainerTitlesResponse{
		Titles: titles,
	}

	return tool.ProtoToBase64(result)
}

// // RpcWearTrainerTitle RPC函数：佩戴训练师称号
// func RpcWearTrainerTitle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	var request MainServer.RpcWearTrainerTitleRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 获取当前已佩戴的称号列表
// 	currentWornTitles := trainer.Decoration.Titles
// 	if currentWornTitles == nil {
// 		currentWornTitles = []*MainServer.TrainerTitleInfo{}
// 	}

// 	// 检查是否已经佩戴了相同的称号
// 	for _, wornTitle := range currentWornTitles {
// 		if wornTitle.Type == request.Type {
// 			return "", runtime.NewError("该称号已经佩戴", 400)
// 		}
// 	}

// 	// 检查佩戴数量限制（最多3个）
// 	if len(currentWornTitles) >= 3 {
// 		return "", runtime.NewError("最多只能佩戴3个称号", 400)
// 	}

// 	// 检查每日称号操作限制
// 	dailyQuota := GetDailyTitleQuota() // 可配置的每日称号配额

// 	// 获取今天的开始时间戳（00:00:00）
// 	now := time.Now()
// 	nowTs := now.Unix()
// 	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()

// 	// 统计今天已经进行的称号操作次数（添加/更换）
// 	todayOperationCount := int32(0)
// 	var maxUpdateTs int64 = 0

// 	for _, wornTitle := range currentWornTitles {
// 		// 找到最大的updateTs
// 		if wornTitle.UpdateTs > maxUpdateTs {
// 			maxUpdateTs = wornTitle.UpdateTs
// 		}

// 		// 如果updateTs是今天的，说明今天有过操作
// 		if wornTitle.UpdateTs >= todayStart {
// 			todayOperationCount++
// 		}
// 	}

// 	// 检查今日操作次数是否超过限制
// 	if todayOperationCount >= dailyQuota {
// 		return "", runtime.NewError(fmt.Sprintf("今日称号操作次数已达上限(%d次)，请明天再试", dailyQuota), 400)
// 	}

// 	// 检查称号是否可以佩戴（消耗使用次数）
// 	err = WearTrainerTitle(ctx, logger, tx, trainer.Id, request.Type)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 获取要佩戴的称号信息
// 	titleInfo, err := GetTrainerTitleInfo(ctx, logger, tx, trainer.Id, request.Type)
// 	if err != nil {
// 		return "", err
// 	}

// 	// 更新称号的updateTs为当前时间（标记为今日操作）
// 	titleInfo.UpdateTs = nowTs

// 	// 添加到已佩戴列表
// 	updatedTitles := append(currentWornTitles, titleInfo)

// 	// 更新训练师的装饰信息
// 	trainer.Decoration.Titles = updatedTitles

// 	// 保存训练师信息
// 	_, err = UpsertTrainer(ctx,  tx, trainer)
// 	if err != nil {
// 		return "", runtime.NewError("Failed to update trainer decoration", 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 返回成功结果
// 	result := &MainServer.RpcWearTrainerTitleResponse{
// 		Success: true,
// 		Message: "称号佩戴成功",
// 	}

// 	return tool.ProtoToBase64(result)
// }

// // RpcUnwearTrainerTitle RPC函数：取消佩戴训练师称号
// func RpcUnwearTrainerTitle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 解析请求参数
// 	var request MainServer.RpcUnwearTrainerTitleRequest
// 	err := tool.Base64ToProto(payload, &request)
// 	if err != nil {
// 		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 创建事务
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to begin transaction", 500)
// 	}
// 	defer tx.Rollback()

// 	// 获取当前已佩戴的称号列表
// 	currentWornTitles := trainer.Decoration.Titles
// 	if currentWornTitles == nil {
// 		return "", runtime.NewError("没有佩戴任何称号", 400)
// 	}

// 	// 查找并移除指定的称号
// 	var updatedTitles []*MainServer.TrainerTitleInfo
// 	found := false
// 	for _, wornTitle := range currentWornTitles {
// 		if wornTitle.Type == request.Type {
// 			found = true
// 			// 跳过这个称号，不添加到新列表中
// 			continue
// 		}
// 		updatedTitles = append(updatedTitles, wornTitle)
// 	}

// 	if !found {
// 		return "", runtime.NewError("该称号未佩戴", 400)
// 	}

// 	// 更新训练师的装饰信息
// 	trainer.Decoration.Titles = updatedTitles

// 	// 保存训练师信息
// 	_, err = UpsertTrainer(ctx,  tx, trainer)
// 	if err != nil {
// 		return "", runtime.NewError("Failed to update trainer decoration", 500)
// 	}

// 	// 提交事务
// 	err = tx.Commit()
// 	if err != nil {
// 		return "", runtime.NewError("Failed to commit transaction", 500)
// 	}

// 	// 返回成功结果
// 	result := &MainServer.RpcUnwearTrainerTitleResponse{
// 		Success: true,
// 		Message: "称号取消佩戴成功",
// 	}

// 	return tool.ProtoToBase64(result)
// }

// RpcGetTitleDailyQuota RPC函数：获取每日称号配额信息
// func RpcGetTitleDailyQuota(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 验证用户身份
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if userID == "" {
// 		return "", runtime.NewError("用户未登录", 401)
// 	}

// 	// 获取训练师信息
// 	trainer := tool.GetActiveTrainerByUid(userID)
// 	if trainer == nil {
// 		return "", runtime.NewError("训练师信息不存在", 404)
// 	}

// 	// 获取当前已佩戴的称号列表
// 	currentWornTitles := trainer.Decoration.Titles
// 	if currentWornTitles == nil {
// 		currentWornTitles = []*MainServer.TrainerTitleInfo{}
// 	}

// 	// 获取今天的开始时间戳（00:00:00）
// 	now := time.Now()
// 	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()

// 	// 统计今天已经进行的称号操作次数
// 	todayOperationCount := int32(0)
// 	for _, wornTitle := range currentWornTitles {
// 		if wornTitle.UpdateTs >= todayStart {
// 			todayOperationCount++
// 		}
// 	}

// 	// 获取每日配额
// 	dailyQuota := GetDailyTitleQuota()

// 	// 返回结果
// 	result := &MainServer.RpcGetTitleDailyQuotaResponse{
// 		DailyQuota:     dailyQuota,
// 		UsedCount:      todayOperationCount,
// 		RemainingCount: dailyQuota - todayOperationCount,
// 	}

// 	return tool.ProtoToBase64(result)
// }
