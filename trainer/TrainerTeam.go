package trainer

import (
	"context"
	"database/sql"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 升级团队的等级
func tryUpTeamLevel(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer) error {
	// trainer.TeamInfo.Contribution += amount
	// _, err := UpsertTrainer(ctx,  tx, trainer)
	// if err != nil {
	// 	trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
	// 	logger.Error("addTeamContribution failed to upsert trainer: %v", err)
	// 	return err
	// }
	return nil
}

// 捐赠Coin到团队贡献值
func tryDonateTeamByCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.Coin < amount {
		return runtime.NewError("金币不足", 400)
	}
	trainer.Coin -= amount
	trainer.TeamInfo.Contribution += amount //先1:1兑换
	_, err := UpsertTrainer(ctx, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

// 尝试消耗team贡献值兑换经验
func tryConsumeTeamContributionToExp(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.TeamInfo.Contribution < amount {
		return runtime.NewError("贡献值不足", 400)
	}
	trainer.TeamInfo.Contribution -= amount
	trainer.TeamInfo.Exp += int32(amount)
	_, err := UpsertTrainer(ctx, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution += amount // 恢复原来的团队贡献值
		trainer.TeamInfo.Exp -= int32(amount)
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

func tryBuyStoreItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, itemNameId string, inventoryType MainServer.InventoryType, count int32, buySiteType MainServer.BuySiteType, teamType MainServer.TrainerTeam) error {
	localItem, exists := item.GetItemByName(itemNameId)
	if !exists {
		return runtime.NewError("物品不存在", 400)
	}
	itemSaleType := MainServer.ItemSaleType_ItemSaleType_Normal
	if buySiteType == MainServer.BuySiteType_BuySiteTypeTeamStore {
		if localItem.TeamCost <= 0 {
			return runtime.NewError("该物品不能通过团队商店购买", 400)
		}
		itemSaleType = MainServer.ItemSaleType_ItemSaleType_Team_Normal
		cost := int64(localItem.TeamCost * count)
		if teamType != trainer.Team {
			cost = int64(float64(cost) * 1.2)
		}
		if trainer.TeamInfo.Contribution < cost {
			return runtime.NewError("贡献值不足", 400)
		}
		trainer.TeamInfo.Contribution -= cost
		err := inventory.AddItemToInventory(ctx, tx, trainer.Id, itemNameId, inventoryType, count, itemSaleType, teamType)
		if err != nil {
			trainer.TeamInfo.Contribution += cost // 恢复原来的团队贡献值
			logger.Error("buyStoreItem team failed to addItemToInventory: %v", err)
			return err
		}
		_, err = UpsertTrainer(ctx, tx, trainer)
		if err != nil {
			trainer.TeamInfo.Contribution += cost // 恢复原来的团队贡献值
			logger.Error("buyStoreItem team failed to upsert trainer: %v", err)
			return err
		}
	} else if buySiteType == MainServer.BuySiteType_BuySiteTypeNormal {
		if localItem.Cost <= 0 {
			return runtime.NewError("该物品不能通过普通商店购买", 400)
		}
		itemSaleType = MainServer.ItemSaleType_ItemSaleType_Normal
		cost := int64(localItem.Cost * count)
		if trainer.Coin < cost {
			return runtime.NewError("金币不足", 400)
		}
		trainer.Coin -= cost
		err := inventory.AddItemToInventory(ctx, tx, trainer.Id, itemNameId, inventoryType, count, itemSaleType, teamType)
		if err != nil {
			trainer.Coin += cost // 恢复原来的金币数量
			logger.Error("buyStoreItem failed to addItemToInventory: %v", err)
			return err
		}
		_, err = UpsertTrainer(ctx, tx, trainer)
		if err != nil {
			trainer.Coin += cost // 恢复原来的金币数量
			logger.Error("buyStoreItem failed to upsert trainer: %v", err)
			return err
		}
	}

	return nil
}

func tryUpdateTeamSummonFluteInfo(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, isCount bool) error {
	if trainer.TeamInfo == nil {
		return runtime.NewError("团队信息不存在", 400)
	}
	if trainer.TeamInfo.SummonFlute == nil {
		trainer.TeamInfo.SummonFlute = &MainServer.SummonFluteInfo{}
	}
	if isCount {
		if trainer.TeamInfo.SummonFlute.CountLevel >= 8 {
			return runtime.NewError("已达到最大等级", 400)
		}
		exp := int32(0)
		switch trainer.TeamInfo.SummonFlute.CountLevel {
		case 0:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_1)
		case 1:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_2)
		case 2:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_3)
		case 3:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_4)
		case 4:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_5)
		case 5:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_6)
		case 6:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_7)
		case 7:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_8)
		}
		if trainer.TeamInfo.Exp < exp {
			return runtime.NewError("团队经验不足", 400)
		}
		trainer.TeamInfo.Exp -= exp
		trainer.TeamInfo.SummonFlute.CountLevel++
		_, err := UpsertTrainer(ctx, tx, trainer)
		if err != nil {
			trainer.TeamInfo.Exp += exp
			trainer.TeamInfo.SummonFlute.CountLevel--
			logger.Error("updateTeamSummonFluteInfo failed to upsert trainer: %v", err)
			return err
		}
	} else {
		if trainer.TeamInfo.SummonFlute.LockLevel >= 8 {
			return runtime.NewError("已达到最大等级", 400)
		}
		exp := int32(0)
		switch trainer.TeamInfo.SummonFlute.LockLevel {
		case 0:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_1)
		case 1:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_2)
		case 2:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_3)
		case 3:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_4)
		case 4:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_5)
		case 5:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_6)
		case 6:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_7)
		case 7:
			exp = int32(MainServer.SummonFluteLevelExp_SUMMON_FLUTE_LEVEL_EXP_8)
		}
		if trainer.TeamInfo.Exp < exp {
			return runtime.NewError("团队经验不足", 400)
		}
		trainer.TeamInfo.Exp -= exp
		trainer.TeamInfo.SummonFlute.LockLevel++
		_, err := UpsertTrainer(ctx, tx, trainer)
		if err != nil {
			trainer.TeamInfo.Exp += exp
			trainer.TeamInfo.SummonFlute.LockLevel--
			logger.Error("updateTeamSummonFluteInfo failed to upsert trainer: %v", err)
			return err
		}
	}
	return nil
}
