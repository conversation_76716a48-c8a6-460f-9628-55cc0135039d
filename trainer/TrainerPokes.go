package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/email"
	"go-nakama-poke/nconst"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"go-nakama-poke/transaction"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// var onePokeBoxVolume = 54

// var oneUserMaxBoxCount = 30

// PublicPokesMetadata 设置或移除用户 Pokes 数据公开
// func PublicPokesMetadata(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

// 	// 解析 payload，确定是否公开
// 	var isPublic bool
// 	if err := json.Unmarshal([]byte(payload), &isPublic); err != nil {
// 		return "", runtime.NewError("无法解析 payload", 400)
// 	}

// 	if isPublic {
// 		// Step 1: 从 poke_boxs 表中读取 index 为 0 且 uid 为当前用户的 PokeBox 数据
// 		var pokeBox MainServer.PokeBox
// 		err := db.QueryRowContext(ctx, `
//             SELECT pokes FROM poke_boxs WHERE uid = $1 AND box_index = 0
//         `, userID).Scan(&pokeBox.Pokes)
// 		if err != nil {
// 			if err == sql.ErrNoRows {
// 				return "", runtime.NewError("未找到用户的 PokeBox 数据", 404)
// 			}
// 			return "", runtime.NewError("无法读取 PokeBox 数据", 500)
// 		}

// 		// Step 2: 解析 Pokes JSON 数据为 []MainServer.BoxPokeInfo
// 		// var pokes []MainServer.BoxPokeInfo
// 		// err = json.Unmarshal([]byte(pokesJSON), &pokes)
// 		// if err != nil {
// 		// 	return "", runtime.NewError("解析 Pokes 数据失败", 500)
// 		// }

// 		// Step 3: 同步 Pokes 数据到 Metadata
// 		err = syncPokesToMetadata(ctx, logger, nk, userID, pokeBox.Pokes)
// 		if err != nil {
// 			return "", err
// 		}

// 		logger.Info("用户 %s 的 %s 数据已公开", userID, MetadataFeildPokes)
// 		return MetadataFeildPokes + " 数据已公开", nil
// 	} else {
// 		// Step 4: 移除 Metadata 中的 pokes 字段
// 		_, err := deleteUserStatusFieldMetadata(ctx, logger, db, nk, userID, MetadataFeildPokes)
// 		if err != nil {
// 			return "", err
// 		}

//			logger.Info("用户 %s 的 %s 数据已设为私密", userID, MetadataFeildPokes)
//			return MetadataFeildPokes + " 数据已设为私密", nil
//		}
//	}
func lastExpInfoRequest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, lastExpInfo *MainServer.LastExpInfoRequest) (*MainServer.Poke, error) {
	// 获取宝可梦信息
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, lastExpInfo.PokeId)
	if pokemon == nil {
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	if len(lastExpInfo.Moves) > 4 {
		return nil, runtime.NewError("move index out of range", 404)
	}
	if pokemon.Extra.LastExpInfo == nil {
		return nil, runtime.NewError("last level up info not found", 404)
	}
	if pokemon.Extra.LastExpInfo.Ts != lastExpInfo.LastExpInfo.Ts ||
		pokemon.Extra.LastExpInfo.OldLevel != lastExpInfo.LastExpInfo.OldLevel ||
		pokemon.Extra.LastExpInfo.OldExp != lastExpInfo.LastExpInfo.OldExp ||
		pokemon.Extra.LastExpInfo.Level != lastExpInfo.LastExpInfo.Level ||
		pokemon.Extra.LastExpInfo.Exp != lastExpInfo.LastExpInfo.Exp {
		return nil, runtime.NewError("last level up info not match", 404)
	}
	// pokemon.Extra.LastExpInfo.IsEvolution != lastExpInfo.LastExpInfo.IsEvolution
	for index, move := range lastExpInfo.Moves {
		if move == "" {
			continue
		}
		hasLastExpMoveId := contains(pokemon.Extra.LastExpInfo.MoveIds, move)
		if !hasLastExpMoveId && !containsSimpleMove(pokemon.Moves, move) {
			return nil, runtime.NewError("pokemon can not learn this move", 404)
		}
		if hasLastExpMoveId {
			pokemon.Extra.LastExpInfo.MoveIds = filter(pokemon.Extra.LastExpInfo.MoveIds, func(item string) bool {
				return item != move
			})
		}
		pokemon.Moves[index] = &MainServer.PokeSimpleMove{Name: move}
	}
	return pokemon, poke.UpdatePokeData(ctx, tx, pokemon)
}
func filter(slice []string, condition func(string) bool) []string {
	var result []string
	for _, value := range slice {
		if condition(value) {
			result = append(result, value)
		}
	}
	return result
}
func containsSimpleMove(slice []*MainServer.PokeSimpleMove, item string) bool {
	for _, v := range slice {
		if v.Name == item {
			return true
		}
	}
	return false
}

func TakeWildPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokemon *MainServer.Poke) error {
	pokemon.Tid = trainer.Id
	pokemon.Evs = &MainServer.PokeStat{} //清空努力值
	pokemon.SysExtra = &MainServer.PokeSysExtra{}
	err := poke.UpdatePokeData(ctx, tx, pokemon)
	if err != nil {
		return err
	}
	_, err = SavePokeToNormalBox(ctx, logger, trainer, tx, pokemon)
	if err != nil {
		return err
	}
	return nil
}
func Payment(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, targetTid int64, coin int64) error {
	if trainer.Coin < coin {
		return runtime.NewError("coin not sale", 404)
	}
	_, err := UpdateTrainerCoinOnly(ctx, logger, tx, trainer.Id, -coin)
	if err != nil {
		return err
	}
	_, err = UpdateTrainerCoinOnly(ctx, logger, tx, targetTid, coin)
	if err != nil {
		return err
	}
	trainer.Coin = trainer.Coin - int64(coin)
	if targetTid > 0 {
		targetTrainer := tool.GetActiveTrainerByTid(targetTid)
		if targetTrainer != nil {
			targetTrainer.Coin += coin
			tool.SendRewardNotification(ctx, logger, nil, targetTrainer, &MainServer.RewardDetail{
				ChannelType: MainServer.RewardChannelType_REWARD_CHANNEL_TYPE_EXCHANGE,
				Money:       int32(coin),
			})
		}
	}
	//扣钱
	// expendCoin(ctx, logger, tx, trainer, coin)
	// _, err := UpsertTrainer(ctx,  tx, trainer)
	// if err != nil {
	// 	trainer.Coin = trainer.Coin + int64(coin)
	// 	return runtime.NewError("update trainer error", 404)
	// }
	return nil
}

func BorrowPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, pokeId int64) (string, error) {
	// 获取当前激活的训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid: %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	pokemon := poke.QueryPokeById(ctx, tx, -1, pokeId)
	if pokemon.TransactionType != MainServer.TransactionType_TransactionType_Market_Sale {
		return "", runtime.NewError("pokemon not sale", 404)
	}
	coin := int64(pokemon.SaleInfo.Price * pokemon.SaleInfo.RentDay)
	err := Payment(ctx, logger, tx, trainer, pokemon.Tid, coin)
	if err != nil {
		return "", err
	}
	pokeinfo, err := TransferRentPoke(ctx, logger, tx, pokemon, trainer)
	if err != nil {
		return "", err
	}
	return tool.ProtoToBase64(pokeinfo)
}

func BuyPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, pokeId int64) (*MainServer.PokeBoxCellInfo, error) {
	// 获取当前激活的训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid: %s", userID)
		return nil, nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	pokemon := poke.QueryPokeById(ctx, tx, -1, pokeId)
	if pokemon.TransactionType != MainServer.TransactionType_TransactionType_Market_Sale {
		return nil, runtime.NewError("pokemon not sale", 404)
	}
	err := Payment(ctx, logger, tx, trainer, pokemon.Tid, int64(pokemon.SaleInfo.Price))
	if err != nil {
		return nil, err
	}
	transaction.RecordPokemonPurchase(ctx, logger, tx, trainer.Id, pokemon.Tid, pokemon.Id, pokemon.Name, int64(pokemon.SaleInfo.Price), 0, "")
	pokeinfo, err := TransferSalePoke(ctx, logger, tx, nk, pokemon, trainer)
	if err != nil {
		return nil, err
	}
	return pokeinfo, nil
	// return "", poke.UpdatePokeData(ctx, tx, pokemon)
}
func TransferRentPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, pokemon *MainServer.Poke, toTrainer *MainServer.Trainer) (*MainServer.PokeBoxCellInfo, error) {
	//sale只有一个index 0
	_, _, err := RemovePokeFromBox(ctx, tx, pokemon.Tid, MainServer.PokeBoxType_sale, 0, -1, pokemon.Id)
	if err != nil {
		return nil, err
	}
	pokemon.TransactionType = MainServer.TransactionType_TransactionType_Market_Borrowing
	pokemon.BorrowInfo.BorrowTs = time.Now().Unix()
	pokemon.BorrowInfo.ReturnTs = pokemon.BorrowInfo.BorrowTs + int64(pokemon.SaleInfo.RentDay*24*60*60)
	pokemon.BorrowInfo.BorrowTrainerId = toTrainer.Id
	pokemon.BorrowInfo.Price = pokemon.SaleInfo.Price
	err = poke.UpdatePokeData(ctx, tx, pokemon)
	if err != nil {
		return nil, err
	}
	// pokemon.SaleInfo.Price = 0
	// pokemon.SaleInfo.CreateTs = 0
	// pokemon.SaleInfo.RentDay = 0
	// pokemon.Tid = toTrainer.Id
	pokeinfo, err := SavePokeToNormalBox(ctx, logger, toTrainer, tx, pokemon)
	if err != nil {
		return nil, err
	}
	return pokeinfo, nil
}
func TransferSalePoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, pokemon *MainServer.Poke, toTrainer *MainServer.Trainer) (*MainServer.PokeBoxCellInfo, error) {
	// 获取当前激活的训练师

	//sale只有一个index 0
	_, _, err := RemovePokeFromBox(ctx, tx, pokemon.Tid, MainServer.PokeBoxType_sale, 0, -1, pokemon.Id)
	if err != nil {
		return nil, err
	}
	pokemon.TransactionType = MainServer.TransactionType_TransactionType_None
	pokemon.SaleInfo.Price = 0
	pokemon.SaleInfo.CreateTs = 0
	pokemon.SaleInfo.RentDay = 0
	pokemon.Tid = toTrainer.Id
	err = poke.UpdatePokeData(ctx, tx, pokemon)
	if err != nil {
		return nil, err
	}
	// pokeinfo, err := SavePokeToNormalBox(ctx, logger, toTrainer, tx, pokemon)
	// if err != nil {
	err = email.SendPokesEmail(ctx, logger, tx, nk, 0, toTrainer.Id, []*MainServer.Poke{pokemon}, MainServer.EmainType_EmainType_Deposit)
	if err != nil {
		return nil, err
	}
	//测试
	return &MainServer.PokeBoxCellInfo{}, nil
	// }
	// return pokeinfo, nil
	// return "", poke.UpdatePokeData(ctx, tx, pokemon)
}
func UpdateFollowPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, param *MainServer.FollowPokeParam) (string, error) {
	// 获取当前激活的训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid: %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	// 如果是移除操作
	if param.Remove {
		for i, poke := range trainer.FollowPoke.Pokes {
			if poke.Id == param.PokeId {
				trainer.FollowPoke.Pokes = append(trainer.FollowPoke.Pokes[:i], trainer.FollowPoke.Pokes[i+1:]...)
				break
			}
		}
		// 更新数据库
		if _, err := UpsertTrainer(ctx, tx, trainer); err != nil {
			return "", fmt.Errorf("failed to update trainer: %v", err)
		}
		return "Pokemon removed from follow list", nil
	}

	// 如果是添加操作，检查 PokeId 是否在 PokeIds 中
	pokeIdStr := fmt.Sprintf("%d", param.PokeId)
	if !tool.Contains(trainer.PokeIds, pokeIdStr) {
		return "", runtime.NewError("PokeId not in trainer's collection", 400)
	}
	for _, poke := range trainer.FollowPoke.Pokes {
		if poke.Id == param.PokeId {
			return "", nil
		}
	}
	// 确保最多只跟随一个 Pokémon
	if len(trainer.FollowPoke.Pokes) >= 1 {
		trainer.FollowPoke.Pokes = trainer.FollowPoke.Pokes[1:]
	}
	// 查询 Pokémon 信息
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, param.PokeId)
	if pokemon == nil {
		return "", fmt.Errorf("failed to query Pokemon")
	}

	// 构造跟随 Pokémon 信息
	info := &MainServer.TrainerFollowPokeInfo{
		Name:   pokemon.Name,
		Id:     pokemon.Id,
		Shiny:  pokemon.Shiny,
		Gender: pokemon.Gender,
	}

	// 添加到跟随列表
	trainer.FollowPoke.Pokes = append(trainer.FollowPoke.Pokes, info)
	_, err := UpsertTrainer(ctx, tx, trainer)
	if err != nil {
		return "", fmt.Errorf("failed to update trainer: %v", err)
	}
	return "Pokemon added to follow list", nil
}

// /pid, item pokeid和道具名
// func UpdatePokeItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 获取当前激活的训练师
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 		logger.Error("未找到用户的 active tid %s", userID)
// 		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
// 	}

// 	// 从 payload 获取 pid
// 	pid, exists := tool.GetIntFromPayload("pid", payload)
// 	if !exists {
// 		return "", runtime.NewError("Not found pid", 404)
// 	}
// 	// Step 1: 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		logger.Error("failed to begin transaction: %v", err)
// 		return "", fmt.Errorf("failed to begin transaction: %w", err)
// 	}
// 	defer tx.Rollback() // 确保失败时回滚事务
// 	// 获取宝可梦信息
// 	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, int64(pid))
// 	if pokemon == nil {
// 		return "", runtime.NewError("Pokemon not found", 400)
// 	}

// 	// 开启事务
// 	// tx, err := db.BeginTx(ctx, nil)
// 	// if err != nil {
// 	// 	logger.Error("事务启动失败: %v", err)
// 	// 	return "", fmt.Errorf("failed to start transaction: %w", err)
// 	// }

// 	// 标志是否需要更新
// 	needsUpdate := false

// 	// 如果 Pokemon 有携带物品，添加到库存
// 	if pokemon.ItemName != "" {
// 		err := inventory.AddItem(ctx, tx, trainer.Id, pokemon.ItemName, int32(1))
// 		if err != nil {
// 			logger.Error("添加物品到库存失败: %v", err)
// 			return "", fmt.Errorf("failed to add item to inventory: %w", err)
// 		}
// 		pokemon.ItemName = "" // 清空 Pokemon 的当前物品
// 		needsUpdate = true
// 	}

// 	// 从 payload 获取新的物品
// 	if itemName, exists := tool.GetStringFromPayload("item", payload); exists {
// 		err := inventory.UseItem(ctx, tx, trainer.Id, itemName, 1)
// 		if err != nil {
// 			logger.Error("使用物品失败: %v", err)
// 			return "", fmt.Errorf("failed to use item: %w", err)
// 		}
// 		pokemon.ItemName = itemName
// 		needsUpdate = true
// 	}

// 	// 如果需要更新宝可梦数据，提交更改
// 	if needsUpdate {
// 		err = poke.UpdatePokeData(ctx, tx, pokemon)
// 		if err != nil {
// 			logger.Error("更新宝可梦数据失败: %v", err)
// 			return "", fmt.Errorf("failed to update Pokemon data: %w", err)
// 		}

// 		// 提交事务
// 		if err := tx.Commit(); err != nil {
// 			logger.Error("事务提交失败: %v", err)
// 			return "", fmt.Errorf("failed to commit transaction: %w", err)
// 		}
// 	}

// 	return "", nil
// }

// 捕捉 Poke 的逻辑 //通过战斗系统来判断使用吧
func TryTakePoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var data struct {
		ItemID string `json:"itemId"`
		PokeID int64  `json:"pokeId"`
	}

	if err := json.Unmarshal([]byte(payload), &data); err != nil {
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_InvalidPayload)
	}

	// 检查 poke 是否可以捕捉
	canCatch := checkPokeCatchable(data.PokeID)
	if !canCatch {
		return "Poke 无法捕捉", nil
	}

	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// tid := tool.GetActiveTrainer(ctx) //GetUserActiveTid(userID)
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return "", nconst.NewGameError(MainServer.ErrorCode_ErrorCode_TrainerNotFound)
	}
	// 尝试使用捕捉道具
	var itemPayload = &MainServer.UseItemInfo{
		ItemName:     data.ItemID,
		TargetPokeId: 0,
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	_, err = TryUsePokeItem(ctx, logger, tx, nk, itemPayload)
	if err != nil {
		logger.Error("使用道具失败: %v", err)
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 根据 poke 状态、种类和道具类型计算捕获概率
	// success := calculateCatchProbability(data.PokeID, data.ItemID)
	// if success {
	// 	//更新
	// 	// 捕捉成功，保存 Poke
	// 	err := savePokeToNormalBox(ctx, logger, nk, tx, trainer.Id, data.PokeID, "PokeName") // PokeName 是占位符，需替换为实际 Poke 名称
	// 	if err != nil {
	// 		return "", err
	// 	}
	// 	logger.Info("成功捕捉 Poke: %s", data.PokeID)
	// 	return "捕捉成功", nil
	// }

	logger.Info("捕捉 Poke 失败: %s", data.PokeID)
	return "捕捉失败", nil
}

// 捕获概率计算函数
func calculateCatchProbability(pokeID int64, itemID string) bool {
	// 这里应该实现捕获概率的实际计算逻辑，基于 Poke 的状态、种类、道具等
	// 目前使用简单的随机概率占位
	// 实际场景中可以根据不同 Poke 的属性和道具属性设置更复杂的概率算法
	return true // 假设一定捕获成功，作为占位逻辑
}

// 检查 Poke 是否可捕捉
func checkPokeCatchable(pokeID int64) bool {
	// 假设检查 poke 是否满足捕捉条件的逻辑
	return true // 占位符：假设 Poke 总是可以捕捉
}

// savePoke 保存新的 Poke 数据，并同步到 Metadata（如果用户的 pokes 是公开的）
// func savePoke(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, pokeID string, name string) error {
// 	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

// 	for i := 0; i < oneUserMaxBoxCount; i++ {
// 		collectionName := userPokesCollectionName + fmt.Sprintf("%d", i)

// 		// 读取用户当前 Pokes 数据
// 		objects, err := nk.StorageRead(ctx, []*runtime.StorageRead{
// 			{
// 				Collection: collectionName,
// 				Key:        userID,
// 			},
// 		})
// 		if err != nil || len(objects) == 0 {
// 			// 如果没有数据，新建一个存储
// 			newPokeData := []map[string]string{
// 				{"pokeID": pokeID, "name": name},
// 			}

// 			// 将新的 poke 存储到 Collection 中
// 			data, _ := json.Marshal(newPokeData)
// 			err := writeToCollection(ctx, nk, collectionName, userID, string(data))
// 			if err != nil {
// 				logger.Error("保存 Poke 失败: %v", err)
// 				return runtime.NewError("保存 Poke 失败", 500)
// 			}

// 			logger.Info("成功保存 Poke 到新 Collection: %s", collectionName)

// 			// 如果是保存到 user_pokes_0 并且 Metadata 中有 pokes 字段，需要同步
// 			if i == 0 {
// 				err = syncPokesToMetadata(ctx, logger, nk, userID, newPokeData)
// 				if err != nil {
// 					logger.Error("同步 Pokes 数据到 Metadata 失败: %v", err)
// 					return err
// 				}
// 				logger.Info("成功将 Pokes 数据同步到用户 Metadata")
// 			}

// 			return nil
// 		}

// 		// 解析当前的 Pokes 数据
// 		var pokes []map[string]string
// 		if err := json.Unmarshal([]byte(objects[0].Value), &pokes); err != nil {
// 			return runtime.NewError("解析 Pokes 数据失败", 500)
// 		}

// 		// 检查该 Collection 中的 Pokes 数量是否小于上限
// 		if len(pokes) < onePokeBoxVolume {
// 			pokes = append(pokes, map[string]string{"pokeID": pokeID, "name": name})

// 			// 将更新后的 Pokes 写回存储
// 			updatedData, _ := json.Marshal(pokes)
// 			err := writeToCollection(ctx, nk, collectionName, userID, string(updatedData))
// 			if err != nil {
// 				logger.Error("保存 Poke 失败: %v", err)
// 				return runtime.NewError("保存 Poke 失败", 500)
// 			}

// 			logger.Info("成功保存 Poke 到 Collection: %s", collectionName)

// 			// 如果是保存到 user_pokes_0 并且 Metadata 中有 pokes 字段，需要同步
// 			if i == 0 {
// 				err = syncPokesToMetadata(ctx, logger, nk, userID, pokes)
// 				if err != nil {
// 					logger.Error("同步 Pokes 数据到 Metadata 失败: %v", err)
// 					return err
// 				}
// 				logger.Info("成功将 Pokes 数据同步到用户 Metadata")
// 			}

// 			return nil
// 		}
// 	}

// 	// 全部盒子已满，保存失败
// 	logger.Warn("用户 %s 的所有 Poke Box 已满", userID)
// 	return runtime.NewError("所有 Poke Box 已满，无法保存", 500)
// }

// 同步 Pokes 数据到 Metadata
// func syncPokesToMetadata(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, userID string, pokesData []map[string]string) error {
// 	// 获取用户当前账户信息
// 	account, err := nk.AccountGetId(ctx, userID)
// 	if err != nil {
// 		return runtime.NewError("无法获取用户账户信息", 500)
// 	}

// 	// 解析现有的 metadata
// 	metadata := make(map[string]interface{})
// 	if err := json.Unmarshal([]byte(account.User.Metadata), &metadata); err != nil {
// 		return runtime.NewError("解析 metadata 失败", 500)
// 	}

// 	// 检查用户是否已经公开了 pokes
// 	if _, exists := metadata["pokes"]; exists {
// 		// 更新 pokes 字段到最新的数据
// 		metadata["pokes"] = pokesData

// 		// 准备更新的字段
// 		updatedFields := map[string]interface{}{
// 			"metadata": metadata,
// 		}

// 		// 调用更新账户的函数
// 		err = updateAccount(ctx, nk, userID, updatedFields)
// 		if err != nil {
// 			logger.Error("更新用户 %s 的 Metadata 失败: %v", userID, err)
// 			return err
// 		}

// 		logger.Info("成功同步 Pokes 数据到用户 %s 的 Metadata", userID)
// 	}

// 	return nil
// }

// func syncPokesToMetadata(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, userID string, pokes []*MainServer.BoxPokeInfo) error {
// 	// 获取用户当前账户信息
// 	account, err := nk.AccountGetId(ctx, userID)
// 	if err != nil {
// 		return runtime.NewError("无法获取用户账户信息", 500)
// 	}
// 	// Step 1: 将 pokes 结构体数组序列化为 JSON
// 	pokesJSON, err := json.Marshal(pokes)
// 	if err != nil {
// 		return runtime.NewError("序列化 Pokes 数据失败", 500)
// 	}
// 	pokesData := string(pokesJSON)
// 	// Step 2: 将 JSON 反序列化为 []map[string]interface{}
// 	// var pokesData []map[string]interface{}
// 	// err = json.Unmarshal(pokesJSON, &pokesData)
// 	// if err != nil {
// 	// 	runtime.NewError("序列化 Pokes 数据失败", 500);
// 	// }
// 	// 解析现有的 metadata
// 	metadata := make(map[string]interface{})
// 	if err := json.Unmarshal([]byte(account.User.Metadata), &metadata); err != nil {
// 		return runtime.NewError("解析 metadata 失败", 500)
// 	}

// 	// 检查用户是否已经公开了 pokes
// 	if _, exists := metadata["pokes"]; exists {
// 		// 更新 pokes 字段到最新的数据
// 		metadata["pokes"] = pokesData

// 		// 准备更新的字段
// 		updatedFields := map[string]interface{}{
// 			"metadata": metadata,
// 		}

// 		// 调用更新账户的函数
// 		err = updateAccount(ctx, nk, userID, updatedFields)
// 		if err != nil {
// 			logger.Error("更新用户 %s 的 Metadata 失败: %v", userID, err)
// 			return err
// 		}

// 		logger.Info("成功同步 Pokes 数据到用户 %s 的 Metadata", userID)
// 	}

// 	return nil
// }

// // Helper function to write data to storage
// func writeToCollection(ctx context.Context, nk runtime.NakamaModule, collectionName string, userID string, data string) error {
// 	_, err := nk.StorageWrite(ctx, []*runtime.StorageWrite{
// 		{
// 			Collection:      collectionName,
// 			Key:             userID,
// 			Value:           data,
// 			PermissionRead:  2, // 仅用户本人可读
// 			PermissionWrite: 0, // 仅用户本人可写
// 		},
// 	})
// 	return err
// }

// func PublicPokesMetadata(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	//payload是一个bool类型，isPublic
// 	//isPublic为true将key为用户id的CollectionName为user_pokes_0的数据拷贝一份到该用户的Metadata的pokes字段中，通过 updateUserStatusMetadata 方法更新
// 	//isPublic为false将Metadata的pokes字段删除，通过deleteUserStatusFieldMetadata
// }

// func TryTakePoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	//payload 会传递一个itemId，pokeId
// 	//这边会判断poke是否可以捕捉
// 	//然后调用func TryUseItemOne(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {}
// 	//这边会调用一个捕获概率的函数，计算是否能捕获成功这个概率的计算会根据poke状态和poke种类、item种类等计算
// 	//捕获成功调用savePoke保存
// 	//捕获成功就返回成功， 捕获失败返回失败
// }

// func savePoke(userId string, pokeId string, name string) (bool, error) {
// 	//读取key为用户id的CollectionName为user_pokes_0的数据，判断数据是否超过6条
// 	//假如超过6条，读取user_pokes_1的数据要是超过onePokeBoxVolume则接着读user_pokes_2，以此类推，但是user_pokes_后面的数量不能超过oneUserMaxBoxCount
// 	//如果没有超过数量，则将poke追加到该Collection中
// 	//最后全部超出数量则保存失败
// }

// BoxLocationInfo 存储宝可梦在盒子中的位置信息
// type BoxLocationInfo struct {
// 	boxType  MainServer.PokeBoxType
// 	boxIndex int32
// 	location string
// }

// // findPokemonBoxLocation 查找宝可梦在训练师盒子中的位置
// func findPokemonBoxLocation(ctx context.Context, tx *sql.Tx, tid int64, pokeId int64) (*BoxLocationInfo, error) {
// 	// 1. 首先检查宝可梦是否在随身盒子中
// 	// 获取训练师的随身宝可梦列表
// 	var pokeIds []string
// 	query := fmt.Sprintf(`
// 		SELECT poke_ids
// 		FROM trainer
// 		WHERE id = $1
// 	`)

// 	var pokeIdsStr string
// 	err := tx.QueryRowContext(ctx, query, tid).Scan(&pokeIdsStr)
// 	if err != nil {
// 		if err == sql.ErrNoRows {
// 			return nil, fmt.Errorf("训练师 %d 不存在", tid)
// 		}
// 		return nil, fmt.Errorf("获取训练师信息失败: %w", err)
// 	}

// 	// 解析宝可梦ID列表
// 	if pokeIdsStr != "" {
// 		pokeIds = strings.Split(pokeIdsStr, ",")
// 	}

// 	// 检查随身盒子
// 	pokeIdStr := strconv.FormatInt(pokeId, 10)
// 	for _, id := range pokeIds {
// 		if id == pokeIdStr {
// 			return &BoxLocationInfo{
// 				boxType:  MainServer.PokeBoxType_around,
// 				boxIndex: 0,
// 				location: id,
// 			}, nil
// 		}
// 	}

// 	// 2. 使用高效的索引查询查找宝可梦在哪个盒子中
// 	query = fmt.Sprintf(`
// 		SELECT pb.type, pb.index, pk.key
// 		FROM poke_boxes pb, jsonb_each(pb.pokes) pk
// 		WHERE pb.tid = $1 AND (pk.value->>'id')::bigint = $2
// 		LIMIT 1
// 	`)

// 	var boxType int32
// 	var boxIndex int32
// 	var location string

// 	err = tx.QueryRowContext(ctx, query, tid, pokeId).Scan(&boxType, &boxIndex, &location)
// 	if err != nil {
// 		if err == sql.ErrNoRows {
// 			return nil, fmt.Errorf("宝可梦 ID %d 不在训练师 %d 的任何盒子中", pokeId, tid)
// 		}
// 		return nil, fmt.Errorf("查询宝可梦位置失败: %w", err)
// 	}

// 	return &BoxLocationInfo{
// 		boxType:  MainServer.PokeBoxType(boxType),
// 		boxIndex: boxIndex,
// 		location: location,
// 	}, nil
// }
