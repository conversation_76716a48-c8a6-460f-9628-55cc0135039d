package trainer

import (
	"context"
	"database/sql"
	"go-nakama-poke/config"
	"go-nakama-poke/nconst"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strconv"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

//	func hatchPoke(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, tx *sql.Tx, trainer *MainServer.Trainer, pokeHatch *MainServer.HatchEggRequest) error {
//		boxType := MainServer.PokeBoxType_hatch
//		// if pokeHatch.IsSpecial {
//		// 	boxType = MainServer.PokeBoxType_specialHatch
//		// }
//		pokeBoxInfo, err := GetPokeBoxInfoByTypeWithLocOrPokeId(ctx, tx, trainer.Id, pokeHatch.EggInfo.PokeBox, pokeHatch.EggInfo.PokeBoxLoc, pokeHatch.PokeId, boxType)
//		if err != nil {
//			return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
//		}
//		cellInfo := &MainServer.PokeBoxCellInfo{
//			Loc:  pokeHatch.EggInfo.PokeBoxLoc,
//			Info: pokeBoxInfo,
//		}
//		if cellInfo.Info.Id != pokeHatch.PokeId {
//			return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
//		}
//		// if pokeBoxInfo.ValueTs < config.HatchPokeInterval {
//		// 	return runtime.NewError("pokemon not hatch", 404)
//		// }
//		// if pokeBoxInfo.ValueTs < 60*30 { //半个小时孵化完成
//		// 	return runtime.NewError("pokemon not hatch", 404)
//		// }
//		// pid := pokeHatch.PokeId
//		// pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pid)
//		// if pokemon == nil {
//		// 	return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
//		// }
//		// pokemon.Egg = false
//		// err = poke.UpdatePokeData(ctx, tx, pokemon)
//		// if err != nil {
//		// 	return err
//		// }
//		// if pokeHatch.TakeOut {
//		// 	_, _, err := RemovePokeFromBox(ctx, tx, trainer.Id, pokeHatch.EggInfo.PokeBox, pokeHatch.PokeId, boxType)
//		// 	if err != nil {
//		// 		return err
//		// 	}
//		// }
//		err = hatchPokeByBoxInfo(ctx, logger, nk, tx, trainer, cellInfo)
//		if err != nil {
//			return err
//		}
//		if pokeHatch.TakeOut {
//			_, _, err := RemovePokeFromBox(ctx, tx, trainer.Id, pokeHatch.EggInfo.PokeBox, pokeHatch.PokeId, boxType)
//			if err != nil {
//				return err
//			}
//		}
//		return nil
//	}
func hatchPokeByBoxInfo(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, tx *sql.Tx, trainer *MainServer.Trainer, loc int32, pokeInfo *MainServer.BoxPokeInfo) error {
	cellInfo := &MainServer.PokeBoxCellInfo{
		Loc:      loc,
		Info:     pokeInfo,
		BoxType:  MainServer.PokeBoxType_hatch,
		BoxIndex: 0,
	}
	if cellInfo.Info.ValueTs < config.HatchPokeSecond { //半个小时孵化完成
		return runtime.NewError("pokemon not hatch", 404)
	}
	pid := cellInfo.Info.Id
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pid)
	if pokemon == nil {
		return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_PokemonNotFound)
	}
	if !pokemon.Egg {
		return nil
	}
	pokemon.Born = &MainServer.BornInfo{
		BornType:         MainServer.BornInfoType_BornInfoType_Egg,
		BornTs:           time.Now().Unix(),
		FristTrainerName: trainer.Name,
	}
	pokemon.Egg = false
	pokemon.Extra.BoxInfo = &MainServer.BoxPokeInfo{}
	pokemon, err := poke.UpdateEggPokeData(ctx, tx, pokemon)
	if err != nil {
		return err
	}
	cellInfo.Poke = pokemon
	tool.SendPokeInfoNotification(ctx, logger, nk, trainer, cellInfo, MainServer.PokeInfoNotificationType_PokeInfoNotificationType_Hatch)
	return nil
}
func UpdateEggTrainerAroundPokes(ctx context.Context, nk runtime.NakamaModule, tx *sql.Tx, logger runtime.Logger, trainerInfo *MainServer.Trainer, valueTime int32) error {
	pokes, ex := tool.GetTrainerAroundPokes(trainerInfo.Id)
	if ex {
		for index, poke := range pokes {
			if poke.Egg {
				if poke.Extra.BoxInfo == nil {
					poke.Extra.BoxInfo = &MainServer.BoxPokeInfo{
						Id:      poke.Id,
						Around:  true,
						ValueTs: 0,
					}
				}
				poke.Extra.BoxInfo.ValueTs = poke.Extra.BoxInfo.ValueTs + valueTime
				hatchPokeByBoxInfo(ctx, logger, nk, tx, trainerInfo, int32(index), poke.Extra.BoxInfo)
			}
		}
	}
	return nil
}
func UpdateEggPokeBox(ctx context.Context, nk runtime.NakamaModule, tx *sql.Tx, logger runtime.Logger, trainer *MainServer.Trainer, pokeBox *MainServer.PokeBox, valueTime int32) error {
	if pokeBox.Type != MainServer.PokeBoxType_hatch {
		return runtime.NewError("not a hatch box", 400)
	}
	for indexStr, pokeInfo := range pokeBox.Pokes {
		index, err := strconv.Atoi(indexStr)
		if err != nil {
			logger.Error("failed to convert index to int: %v", err)
			continue
		}
		if index >= config.DefaultHatchBoxPokeCount && trainer.SpecialRight != MainServer.TrainerSpecialRight_TrainerPremium {
			// logger.Error("no permission to modify special hatch box")
			continue
		}
		pokeInfo.ValueTs = pokeInfo.ValueTs + valueTime
		err = hatchPokeByBoxInfo(ctx, logger, nk, tx, trainer, int32(index), pokeInfo)
		if err != nil {
			logger.Error("failed to hatch poke: %v", err)
			continue
		}
		// if pokeInfo.ValueTs <= 0 {
		// 	pokeInfo.ValueTs = 0
		// }
		pokeBox.Pokes[indexStr] = pokeInfo
	}
	_, err := UpdatePokeBoxWithOptimisticLock(ctx, tx, pokeBox)
	if err != nil {
		logger.Error("failed to update box: %v", err)
	}
	return err
}
func checkHatchBoxAccess(trainer *MainServer.Trainer, opPoke *MainServer.Poke, index int32, isWrite bool) error {
	if !opPoke.Egg && isWrite {
		return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_NotAnEgg)
	}
	// if index >= config.DefaultHatchBoxPokeCount && isWrite && trainer.SpecialRight != MainServer.TrainerSpecialRight_TrainerPremium {
	// 	return nconst.NewGameError(MainServer.ErrorCode_ErrorCode_SpecialRightRequired, "no permission to modify special hatch box")
	// }
	return nil
}

func StartUpdateHatchLoop(ctx context.Context, db *sql.DB, logger runtime.Logger, nk runtime.NakamaModule) {
	ticker := time.NewTicker(config.UpdateHatchPokeSecond * time.Second) //3分钟更新一次
	go func() {
		for {
			select {
			case <-ticker.C:
				tx, err := db.BeginTx(ctx, nil)
				if err != nil {
					logger.Error("failed to begin transaction: %v", err)
					return
				}
				trainers := tool.GetAllActiveTrainers()
				for _, trainerInfo := range trainers {
					UpdateEggTrainerAroundPokes(ctx, nk, tx, logger, trainerInfo, config.UpdateHatchPokeSecond)
					// trainerInfo
					box, err := GetPokeBoxByIndexAndType(ctx, tx, trainerInfo.Id, 0, MainServer.PokeBoxType_hatch)
					if err != nil {
						logger.Error("failed to retrieve source box: %v", err)
						continue
					}
					err = UpdateEggPokeBox(ctx, nk, tx, logger, trainerInfo, box, config.UpdateHatchPokeSecond)
					if err != nil {
						logger.Error("failed to update box: %v", err)
						continue
					}
				}
				if err := tx.Commit(); err != nil {
					logger.Error("failed to commit transaction: %v", err)
					return
				}
			case <-ctx.Done():
				return
			}
		}
	}()
}
