package trainer

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// var instanceMapInfos map[MainServer.InstanceMapTpye]*MainServer.LocalItem

// LoadPokemonInfos 加载 Pokedex 数据到全局变量 pokemonInfos
func LoadInstanceMapInfos() {
	// 读取 JSON 文件
	// data, err := os.ReadFile("/nakama/data/instanceMapItems.json")
	// if err != nil {
	// 	log.Fatalf("Failed to read instanceMapItems.json file: %v", err)
	// }
	// instanceMapItems := make(map[string]string)
	// instanceMapInfos := make(map[MainServer.InstanceMapTpye]*MainServer.LocalItem)
	// // 解析 JSON 数据直接到全局变量
	// if err := json.Unmarshal(data, &instanceMapItems); err != nil {
	// 	log.Fatalf("Failed to parse instanceMapItems.json: %v", err)
	// }
	// for mapTypeString, itemName := range instanceMapItems {
	// 	mapTypeInt, err := strconv.Atoi(mapTypeString)
	// 	if err != nil {
	// 		log.Fatalf("Failed to mapTypeInt: %v", err)
	// 	}
	// 	localItem, ex := item.GetItemByKeyAndName(MainServer.InventoryType_inventory_quest, itemName)
	// 	if ex {
	// 		instanceMapInfos[MainServer.InstanceMapTpye(int32(mapTypeInt))] = localItem
	// 	}
	// }
	// log.Printf("Successfully loaded %d Pokémon entries into pokemonInfos.", len(instanceMapInfos))
}

func checkCanEnterInstanceMap(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, trainer *MainServer.Trainer, mapTpye MainServer.MainLandType) bool {
	// item, exists := instanceMapInfos[mapTpye]
	// if exists {
	// 	itemInfo, exists := trainer.Items[item.Name]
	// 	if exists {
	// 		nowTs := time.Now().Unix()
	// 		if nowTs > itemInfo.ExpireTs {
	// 			delete(trainer.Items, item.Name)
	// 			UpsertTrainer(ctx,  tx, trainer)
	// 			return false
	// 		}
	// 		return true
	// 	}
	// }
	return false
}
