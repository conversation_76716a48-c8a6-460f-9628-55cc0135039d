package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"math/rand"

	"go-nakama-poke/equipment"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RewardResult 奖励结果（临时结构体，等待 proto 定义）
// type RewardResult struct {
// 	RewardId  string             `json:"reward_id"`
// 	BaseMoney int32              `json:"base_money"`
// 	Rewards   []*GeneratedReward `json:"rewards"` // 统一的奖励数组
// }

// // GeneratedReward 生成的奖励（临时结构体，等待 proto 定义）
// type GeneratedReward struct {
// 	RewardType     MainServer.RewardType `json:"reward_type"`
// 	RewardValueStr string                `json:"reward_value_str"`
// 	Count          int32                 `json:"count"`
// }

// RewardManager 奖励管理器（从内存中读取配置）
var RewardConfigs map[string]*MainServer.RewardInfo

// InitRewards 初始化奖励配置（从JSON配置文件加载到内存）
func InitRewards() {
	// 这里应该从JSON配置文件加载奖励数据到内存
	// 目前使用空的map，实际使用时需要加载配置
	RewardConfigs = make(map[string]*MainServer.RewardInfo)
}

// GenerateReward 生成奖励（新的奖励系统）(不可用，没有去读取配置)
func GenerateRewardByNameId(ctx context.Context, logger runtime.Logger, trainerInfo *MainServer.Trainer, rewardId string, channelType MainServer.RewardChannelType) (*MainServer.RewardResult, error) {
	// 从配置中获取奖励信息
	rewardInfo, exists := RewardConfigs[rewardId]
	if !exists {
		return nil, fmt.Errorf("reward config not found for id: %s", rewardId)
	}
	return GrantRewardByInfo(ctx, logger, trainerInfo, rewardInfo, channelType)
}
func GrantRewardByInfo(ctx context.Context, logger runtime.Logger, trainerInfo *MainServer.Trainer, rewardInfo *MainServer.RewardInfo, channelType MainServer.RewardChannelType) (*MainServer.RewardResult, error) {
	// 生成各组奖励
	allRewards := generateRewardGroup(rewardInfo.AllRewards)                    // 全部发放
	oneRewards := generateLimitedRewardGroup(rewardInfo.OneRewards, 1)          // 最多选1个
	twoRewards := generateLimitedRewardGroup(rewardInfo.TwoRewards, 2)          // 最多选2个
	threeRewards := generateLimitedRewardGroup(rewardInfo.ThreeRewards, 3)      // 最多选3个
	spAllRewards := generateSpRewardGroup(trainerInfo, rewardInfo.SpAllRewards) // 特别的all组奖励(比如徽章之类的只发一次)
	// 合并所有奖励到统一数组
	var finalRewards []*MainServer.GeneratedReward
	finalRewards = append(finalRewards, allRewards...)
	finalRewards = append(finalRewards, oneRewards...)
	finalRewards = append(finalRewards, twoRewards...)
	finalRewards = append(finalRewards, threeRewards...)

	result := &MainServer.RewardResult{
		RewardId:    rewardInfo.RewardId,
		BaseMoney:   generateBaseMoney(rewardInfo),
		Rewards:     finalRewards,
		SpRewards:   spAllRewards,
		ChannelType: channelType,
	}

	logger.Info("Generated reward for id %s: base_money=%d", rewardInfo.RewardId, result.BaseMoney)
	return result, nil
}
func generateSpRewardGroup(trainerInfo *MainServer.Trainer, rewardValues []*MainServer.RewardValue) []*MainServer.GeneratedReward { //全部
	var results []*MainServer.GeneratedReward
	//优化下面的双重循环，可以先将trainerInfo.Badges.Badges进行一次遍历，将徽章类型作为key，徽章信息作为value存入一个map中
	badgeMap := make(map[int32]*MainServer.TrainerBadge)
	for _, badge := range trainerInfo.Badges.Badges {
		badgeMap[int32(badge.Type)] = badge
	}
	for _, rewardValue := range rewardValues {
		// 检查掉落率
		// if !checkDropRate(rewardValue.DropRate) {
		// 	continue
		// }
		switch rewardValue.RewardType {
		case MainServer.RewardType_RewardType_Sp_Badge:
			if _, ok := badgeMap[rewardValue.RewardValueInt]; ok && rewardValue.RewardValueInt > 0 {
				continue
			}
			// for _, v := range trainerInfo.Badges.Badges {
			// 	if int32(v.Type) == rewardValue.RewardValueInt {
			// 		continue
			// 	}
			// }
		case MainServer.RewardType_RewardType_Sp_Champion: //暂无
			continue
		default:
			continue
		}
		// 生成奖励数量
		count := generateRewardCount(rewardValue)
		if count <= 0 {
			continue
		}

		// 创建生成的奖励
		generatedReward := &MainServer.GeneratedReward{
			RewardType:     rewardValue.RewardType,
			RewardValueStr: rewardValue.RewardValueStr,
			RewardValueInt: rewardValue.RewardValueInt,
			Count:          count,
		}

		results = append(results, generatedReward)
	}
	return results
}

// generateBaseMoney 生成基础金币奖励
func generateBaseMoney(rewardInfo *MainServer.RewardInfo) int32 {
	if rewardInfo.RewardBaseMinMoney >= rewardInfo.RewardBaseMaxMoney {
		return rewardInfo.RewardBaseMinMoney
	}

	diff := rewardInfo.RewardBaseMaxMoney - rewardInfo.RewardBaseMinMoney
	return rewardInfo.RewardBaseMinMoney + int32(rand.Intn(int(diff)+1))
}

// generateRewardGroup 生成奖励组（全部发放）
func generateRewardGroup(rewardValues []*MainServer.RewardValue) []*MainServer.GeneratedReward {
	var results []*MainServer.GeneratedReward

	for _, rewardValue := range rewardValues {
		// 检查掉落率
		if !checkDropRate(rewardValue.DropRate) {
			continue
		}

		// 生成奖励数量
		count := generateRewardCount(rewardValue)
		if count <= 0 {
			continue
		}

		// 创建生成的奖励
		generatedReward := &MainServer.GeneratedReward{
			RewardType:     rewardValue.RewardType,
			RewardValueStr: rewardValue.RewardValueStr,
			Count:          count,
		}

		results = append(results, generatedReward)
	}

	return results
}

// generateLimitedRewardGroup 生成限制数量的奖励组（最多选择指定数量）
func generateLimitedRewardGroup(rewardValues []*MainServer.RewardValue, maxCount int) []*MainServer.GeneratedReward {
	var candidates []*MainServer.GeneratedReward

	// 先生成所有可能的奖励候选
	for _, rewardValue := range rewardValues {
		// 检查掉落率
		if !checkDropRate(rewardValue.DropRate) {
			continue
		}

		// 生成奖励数量
		count := generateRewardCount(rewardValue)
		if count <= 0 {
			continue
		}

		// 创建生成的奖励
		generatedReward := &MainServer.GeneratedReward{
			RewardType:     rewardValue.RewardType,
			RewardValueStr: rewardValue.RewardValueStr,
			Count:          count,
		}

		candidates = append(candidates, generatedReward)
	}

	// 如果候选数量小于等于限制数量，返回全部
	if len(candidates) <= maxCount {
		return candidates
	}

	// 随机选择指定数量的奖励
	var results []*MainServer.GeneratedReward
	selectedIndices := make(map[int]bool)

	for len(results) < maxCount {
		index := rand.Intn(len(candidates))
		if !selectedIndices[index] {
			selectedIndices[index] = true
			results = append(results, candidates[index])
		}
	}

	return results
}

// checkDropRate 检查掉落率（0-100）
func checkDropRate(dropRate int32) bool {
	if dropRate <= 0 {
		return false
	}
	if dropRate >= 100 {
		return true
	}

	// 生成0-99的随机数
	randomValue := rand.Intn(100)
	return randomValue < int(dropRate)
}

// generateRewardCount 生成奖励数量
func generateRewardCount(rewardValue *MainServer.RewardValue) int32 {
	minCount := rewardValue.RewardMinCount
	maxCount := rewardValue.RewardMaxCount

	if minCount <= 0 {
		return 0
	}
	if minCount >= maxCount {
		return minCount
	}

	diff := maxCount - minCount
	return minCount + int32(rand.Intn(int(diff)+1))
}

// GrantReward 发放奖励（留下接口，待完善）
func GrantReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, rewardResult *MainServer.RewardResult) error {
	if rewardResult == nil {
		return nil
	}
	oldCoin := trainer.Coin
	rewardDetail := &MainServer.RewardDetail{
		ChannelType: rewardResult.ChannelType,
	}
	// 发放基础金币
	if rewardResult.BaseMoney > 0 {
		trainer.Coin += int64(rewardResult.BaseMoney)
		rewardDetail.Money = rewardResult.BaseMoney
		logger.Info("Granted base money %d to trainer %d", rewardResult.BaseMoney, trainer.Id)
	}

	// 发放所有奖励
	for _, reward := range rewardResult.Rewards {
		err := grantGeneratedReward(ctx, logger, tx, trainer, reward, rewardDetail)
		if err != nil {
			logger.Error("Failed to grant reward: %v", err)
			return err
		}
	}

	// 更新训练师信息
	_, err := UpsertTrainer(ctx, tx, trainer)
	if err != nil {
		trainer.Coin = oldCoin // 回滚金币变更
		logger.Error("Failed to update trainer: %v", err)
		return err
	}
	tool.SendRewardNotification(ctx, logger, nil, trainer, rewardDetail)
	return nil
}

// grantGeneratedReward 发放生成的奖励
func grantGeneratedReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, reward *MainServer.GeneratedReward, rewardDetail *MainServer.RewardDetail) error {
	switch reward.RewardType {
	case MainServer.RewardType_RewardType_Pokemon:
		poke, err := grantPokemonReward(ctx, logger, tx, trainer, reward.RewardValueStr, reward.Count)
		if err == nil && poke != nil {
			rewardDetail.Pokes = append(rewardDetail.Pokes, poke)
		}
		return err
	case MainServer.RewardType_RewardType_Item:
		item, err := grantItemReward(ctx, logger, tx, trainer, reward.RewardValueStr, reward.Count)
		if err == nil && item != nil {
			rewardDetail.Inventories = append(rewardDetail.Inventories, item)
		}
		return err
	case MainServer.RewardType_RewardType_Equip:
		equit, err := grantEquitReward(ctx, logger, tx, trainer, reward.RewardValueStr, reward.Count)
		if err == nil && equit != nil {
			rewardDetail.Equipments = append(rewardDetail.Equipments, equit)
		}
		return err
	case MainServer.RewardType_RewardType_Cloth:
		cloth, err := grantClothReward(ctx, logger, tx, trainer, reward.RewardValueStr, reward.Count)
		if err == nil && cloth != nil {
			rewardDetail.Clothes = append(rewardDetail.Clothes, cloth)
		}
		return err

	case MainServer.RewardType_RewardType_Money:
		trainer.Coin += int64(reward.Count)
		rewardDetail.Money += reward.Count
		logger.Info("Granted money %d to trainer %d", reward.Count, trainer.Id)
		return nil
	case MainServer.RewardType_RewardType_Sp_Badge:
		trainer.Badges.Badges = append(trainer.Badges.Badges, &MainServer.TrainerBadge{Type: MainServer.TrainerBadgeType(reward.RewardValueInt)})
		rewardDetail.Badges = append(rewardDetail.Badges, &MainServer.TrainerBadge{Type: MainServer.TrainerBadgeType(reward.RewardValueInt)})
		return nil
	default:
		logger.Error("Unknown reward type: %v", reward.RewardType)
		return fmt.Errorf("unknown reward type: %v", reward.RewardType)
	}
}

// grantPokemonReward 发放宝可梦奖励（新版本）
func grantPokemonReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokemonId string, count int32) (*MainServer.Poke, error) {
	// TODO: 调用宝可梦系统的接口来发放宝可梦
	// 示例：
	// for i := int32(0); i < count; i++ {
	//     err := pokemon.AddPokemonToTrainer(ctx, logger, tx, trainer.Id, pokemonId)
	//     if err != nil {
	//         return err
	//     }
	// }

	logger.Info("Pokemon reward granted to trainer %d, pokemon_id: %s, count: %d", trainer.Id, pokemonId, count)
	return nil, nil
}

// grantItemReward 发放道具奖励（新版本）
func grantItemReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, itemId string, count int32) (*MainServer.Inventory, error) {
	// TODO: 调用道具系统的接口来发放道具
	// 示例：
	// err := inventory.AddItem(ctx, logger, tx, trainer.Id, itemId, count)
	// if err != nil {
	//     return err
	// }

	logger.Info("Item reward granted to trainer %d, item_id: %s, count: %d", trainer.Id, itemId, count)
	return nil, nil
}
func grantEquitReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, equitConfigId string, count int32) (*MainServer.Equipment, error) {
	equipmentInfo, err := equipment.GenerateEquipmentInfo(equitConfigId)
	if err != nil {
		return nil, err
	}
	equipmentInfo.Tid = trainer.Id
	logger.Info("Item reward granted to trainer %d, equit_id: %s, count: %d", trainer.Id, equipmentInfo.EquipmentName, count)
	err = equipment.AddEquipment(ctx, tx, equipmentInfo)
	if err != nil {
		return nil, err
	}
	return equipmentInfo, err
}

// grantClothReward 发放服装奖励（新版本）
func grantClothReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, clothId string, count int32) (*MainServer.TrainerCloth, error) {
	// TODO: 调用服装系统的接口来发放服装
	// 示例：
	// for i := int32(0); i < count; i++ {
	//     err := cloth.AddClothToTrainer(ctx, logger, tx, trainer.Id, clothId)
	//     if err != nil {
	//         return err
	//     }
	// }

	logger.Info("Cloth reward granted to trainer %d, cloth_id: %s, count: %d", trainer.Id, clothId, count)
	return nil, nil
}

// // GrantQuestReward 发放任务奖励（使用新的奖励系统）
// func GrantQuestReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, rewardInfo *MainServer.RewardInfo) error {
// 	if rewardInfo == nil {
// 		return nil // 无奖励
// 	}

// 	// 生成奖励
// 	rewardResult, err := GenerateRewardFromInfo(ctx, logger, rewardInfo)
// 	if err != nil {
// 		logger.Error("Failed to generate reward: %v", err)
// 		return err
// 	}

// 	// 发放奖励
// 	err = GrantReward(ctx, logger, tx, trainer, rewardResult)
// 	if err != nil {
// 		logger.Error("Failed to grant reward: %v", err)
// 		return err
// 	}

// 	logger.Info("Successfully granted quest reward to trainer %d", trainer.Id)
// 	return nil
// }

// // GenerateRewardFromInfo 直接从 RewardInfo 生成奖励（不需要从配置中查找）
// func GenerateRewardFromInfo(ctx context.Context, logger runtime.Logger, rewardInfo *MainServer.RewardInfo) (*MainServer.RewardResult, error) {
// 	if rewardInfo == nil {
// 		return nil, fmt.Errorf("reward info is nil")
// 	}

// 	// 生成各组奖励
// 	allRewards := generateRewardGroup(rewardInfo.AllRewards)               // 全部发放
// 	oneRewards := generateLimitedRewardGroup(rewardInfo.OneRewards, 1)     // 最多选1个
// 	twoRewards := generateLimitedRewardGroup(rewardInfo.TwoRewards, 2)     // 最多选2个
// 	threeRewards := generateLimitedRewardGroup(rewardInfo.ThreeRewards, 3) // 最多选3个

// 	// 合并所有奖励到统一数组
// 	var finalRewards []*MainServer.GeneratedReward
// 	finalRewards = append(finalRewards, allRewards...)
// 	finalRewards = append(finalRewards, oneRewards...)
// 	finalRewards = append(finalRewards, twoRewards...)
// 	finalRewards = append(finalRewards, threeRewards...)

// 	result := &MainServer.RewardResult{
// 		RewardId:  rewardInfo.RewardId,
// 		BaseMoney: generateBaseMoney(rewardInfo),
// 		Rewards:   finalRewards,
// 	}

// 	logger.Info("Generated reward from info: reward_id=%s, base_money=%d, all=%d, one=%d, two=%d, three=%d, total_rewards=%d",
// 		result.RewardId, result.BaseMoney,
// 		len(allRewards), len(oneRewards), len(twoRewards), len(threeRewards), len(result.Rewards))

// 	return result, nil
// }

// // checkRewardProbability 检查奖励概率
// func checkRewardProbability(rate float32) bool {
// 	if rate <= 0 {
// 		return false
// 	}
// 	if rate >= 1.0 {
// 		return true
// 	}

// 	// 生成0-1之间的随机数
// 	randomValue := rand.Float32()
// 	return randomValue <= rate
// }

// // GetRewardPreview 获取奖励预览
// func GetRewardPreview(rewardId string) (*MainServer.RewardInfo, error) {
// 	if rewardId == "" {
// 		return nil, nil
// 	}

// 	rewardInfo, exists := RewardConfigs[rewardId]
// 	if !exists {
// 		return nil, fmt.Errorf("reward config not found for id: %s", rewardId)
// 	}

// 	return rewardInfo, nil
// }

// // CalculateRewardPreview 计算奖励预览
// func CalculateRewardPreview(rewardId string) (map[MainServer.RewardType]RewardRange, error) {
// 	rewardInfo, err := GetRewardPreview(rewardId)
// 	if err != nil {
// 		return nil, err
// 	}
// 	if rewardInfo == nil {
// 		return nil, nil
// 	}

// 	preview := make(map[MainServer.RewardType]RewardRange)

// 	// 处理所有奖励组
// 	allGroups := [][]*MainServer.RewardValue{
// 		rewardInfo.AllRewards,
// 		rewardInfo.OneRewards,
// 		rewardInfo.TwoRewards,
// 		rewardInfo.ThreeRewards,
// 	}

// 	for _, group := range allGroups {
// 		for _, rewardValue := range group {
// 			rewardType := rewardValue.RewardType

// 			// 计算数量范围
// 			minCount, maxCount := calculateCountRangeNew(rewardValue)

// 			if existing, exists := preview[rewardType]; exists {
// 				// 如果已存在该类型奖励，累加范围
// 				existing.MinCount += minCount
// 				existing.MaxCount += maxCount
// 				// 计算组合概率
// 				dropRateFloat := float32(rewardValue.DropRate) / 100.0
// 				existing.Probability = 1.0 - (1.0-existing.Probability)*(1.0-dropRateFloat)
// 				preview[rewardType] = existing
// 			} else {
// 				preview[rewardType] = RewardRange{
// 					MinCount:    minCount,
// 					MaxCount:    maxCount,
// 					Probability: float32(rewardValue.DropRate) / 100.0,
// 				}
// 			}
// 		}
// 	}

// 	return preview, nil
// }

// // RewardRange 奖励范围
// type RewardRange struct {
// 	MinCount    int32   `json:"min_count"`
// 	MaxCount    int32   `json:"max_count"`
// 	Probability float32 `json:"probability"`
// }

// // calculateCountRangeNew 计算数量范围
// func calculateCountRangeNew(rewardValue *MainServer.RewardValue) (int32, int32) {
// 	minCount := rewardValue.RewardMinCount
// 	maxCount := rewardValue.RewardMaxCount

// 	if minCount <= 0 {
// 		return 0, 0
// 	}
// 	if minCount >= maxCount {
// 		return minCount, minCount
// 	}

// 	return minCount, maxCount
// }

// // GetFinalRewardResult 获取最终计算好的奖励结果（用于展示）
// func GetFinalRewardResult(ctx context.Context, logger runtime.Logger, rewardInfo *MainServer.RewardInfo) (*MainServer.RewardResult, error) {
// 	if rewardInfo == nil {
// 		return nil, fmt.Errorf("reward info is nil")
// 	}

// 	// 生成最终奖励结果
// 	result, err := GenerateRewardFromInfo(ctx, logger, rewardInfo)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 记录详细的奖励信息
// 	logger.Info("=== 最终奖励结果 ===")
// 	logger.Info("奖励ID: %s", result.RewardId)
// 	logger.Info("基础金币: %d", result.BaseMoney)

// 	logger.Info("最终奖励列表: %d个", len(result.Rewards))
// 	for i, reward := range result.Rewards {
// 		logger.Info("  [%d] 类型: %v, 值: %s, 数量: %d", i+1, reward.RewardType, reward.RewardValueStr, reward.Count)
// 	}

// 	logger.Info("总奖励数量: %d个", len(result.Rewards))
// 	logger.Info("==================")

// 	return result, nil
// }

// GetRewardSummary 获取奖励摘要信息
// func GetRewardSummary(result *MainServer.RewardResult) map[string]interface{} {
// 	if result == nil {
// 		return nil
// 	}

// 	summary := make(map[string]interface{})
// 	summary["reward_id"] = result.RewardId
// 	summary["base_money"] = result.BaseMoney

// 	// 统计各类型奖励数量
// 	rewardTypeCounts := make(map[string]int32)

// 	for _, reward := range result.Rewards {
// 		rewardTypeStr := reward.RewardType.String()
// 		rewardTypeCounts[rewardTypeStr] += reward.Count
// 	}

// 	summary["reward_type_counts"] = rewardTypeCounts
// 	summary["total_reward_items"] = len(result.Rewards)

// 	return summary
// }

// init 初始化随机数种子
// 注意：Go 1.20+ 不再需要手动设置随机数种子
func init() {
	// rand.Seed(time.Now().UnixNano()) // 已弃用
}
